[ERROR][2025-08-19 01:37:05][app.py:66] - App startup failed!
[ERROR][2025-08-19 01:37:05][app.py:67] - key auto_reply_text not in available_setting
Traceback (most recent call last):
  File "E:\Desktop\123\dify-on-wechat\app.py", line 46, in run
    load_config()
  File "E:\Desktop\123\dify-on-wechat\config.py", line 316, in load_config
    config = Config(json.loads(config_str))
  File "E:\Desktop\123\dify-on-wechat\config.py", line 224, in __init__
    self[k] = v
  File "E:\Desktop\123\dify-on-wechat\config.py", line 235, in __setitem__
    raise Exception("key {} not in available_setting".format(key))
Exception: key auto_reply_text not in available_setting
[DEBUG][2025-08-19 01:37:45][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 01:37:45][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '你好啊'}
[INFO][2025-08-19 01:37:45][config.py:266] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-19 01:37:45][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 01:37:45][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[WARNING][2025-08-19 01:37:45][audio_convert.py:20] - import pilk failed, silk voice conversion will not be supported. Try: pip install pilk
[ERROR][2025-08-19 01:37:45][app.py:66] - App startup failed!
[ERROR][2025-08-19 01:37:45][app.py:67] - No module named 'pilk'
Traceback (most recent call last):
  File "E:\Desktop\123\dify-on-wechat\app.py", line 61, in run
    start_channel(channel_name)
  File "E:\Desktop\123\dify-on-wechat\app.py", line 29, in start_channel
    channel = channel_factory.create_channel(channel_name)
  File "E:\Desktop\123\dify-on-wechat\channel\channel_factory.py", line 43, in create_channel
    from channel.wework.wework_channel import WeworkChannel
  File "E:\Desktop\123\dify-on-wechat\channel\wework\wework_channel.py", line 14, in <module>
    from channel.wework.wework_message import *
  File "E:\Desktop\123\dify-on-wechat\channel\wework\wework_message.py", line 6, in <module>
    import pilk
ModuleNotFoundError: No module named 'pilk'
[DEBUG][2025-08-19 01:38:06][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 01:38:06][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '你好啊'}
[INFO][2025-08-19 01:38:06][config.py:266] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-19 01:38:06][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 01:38:06][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 01:38:07][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 01:38:07][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 01:38:07][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 01:38:07][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 01:38:07][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin GODCMD not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin KEYWORD not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin BANWORDS not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin LINKAI not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin ROLE not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin DUNGEON not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin CUSTOMDIFYAPP not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin BDUNIT not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin HELLO not found in pconfig, adding to pconfig...
[INFO][2025-08-19 01:38:07][plugin_manager.py:123] - Plugin FINISH not found in pconfig, adding to pconfig...
[DEBUG][2025-08-19 01:38:07][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': True, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': True, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 01:38:07][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=False
[DEBUG][2025-08-19 01:38:07][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf=None
[INFO][2025-08-19 01:38:07][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === 8Zs%1TqQ#P$X ===。
[INFO][2025-08-19 01:38:07][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 01:38:07][keyword.py:30] - [keyword]不存在配置文件E:\Desktop\123\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 01:38:07][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 01:38:07][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 01:38:07][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\banwords\config.json, exist=False
[DEBUG][2025-08-19 01:38:07][plugin.py:28] - loading plugin config, plugin_name=Banwords, conf=None
[WARNING][2025-08-19 01:38:07][banwords.py:54] - [Banwords] init failed, ignore or see https://github.com/zhayujie/chatgpt-on-wechat/tree/master/plugins/banwords .
[WARNING][2025-08-19 01:38:07][plugin_manager.py:151] - Failed to init BANWORDS, diabled. [Errno 2] No such file or directory: 'E:\\Desktop\\123\\dify-on-wechat\\plugins\\banwords\\banwords.txt'
[DEBUG][2025-08-19 01:38:07][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 01:38:07][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 01:38:07][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 01:38:07][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\bdunit\config.json, exist=False
[DEBUG][2025-08-19 01:38:07][plugin.py:28] - loading plugin config, plugin_name=BDunit, conf=None
[WARNING][2025-08-19 01:38:07][bdunit.py:42] - [BDunit] init failed, ignore 
[WARNING][2025-08-19 01:38:07][plugin_manager.py:151] - Failed to init BDUNIT, diabled. config.json not found
[DEBUG][2025-08-19 01:38:07][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 01:38:07][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 01:38:07][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 01:38:07][hello.py:38] - [Hello] inited
[INFO][2025-08-19 01:38:07][finish.py:23] - [Finish] inited
[INFO][2025-08-19 01:38:07][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 01:38:09][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 01:38:09][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-19 01:39:09][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 01:39:36][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ2MqNxQYY76TDoYWAgAMgAw==', 'at_list': [], 'content': 'PAMZHHZ5250815EP0049', 'content_type': 2, 'conversation_id': 'S:****************_1688853356875896', 'is_pc': 1, 'local_id': '38376', 'receiver': '1688853356875896', 'send_time': '1755538776', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774143'}, 'type': 11041}
[DEBUG][2025-08-19 01:39:36][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:40:17][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 01:40:17][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '你好啊'}
[INFO][2025-08-19 01:40:17][config.py:266] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-19 01:40:17][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 01:40:17][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 01:40:17][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 01:40:17][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 01:40:17][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 01:40:17][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 01:40:17][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 01:40:17][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 01:40:17][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 01:40:17][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 01:40:17][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === MFFX?T?oo6VH ===。
[INFO][2025-08-19 01:40:17][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 01:40:17][keyword.py:35] - [keyword]加载配置文件E:\Desktop\123\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 01:40:17][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 01:40:17][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 01:40:17][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 01:40:17][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 01:40:17][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 01:40:17][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 01:40:17][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 01:40:17][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 01:40:17][hello.py:38] - [Hello] inited
[INFO][2025-08-19 01:40:17][finish.py:23] - [Finish] inited
[INFO][2025-08-19 01:40:17][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 01:40:35][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 01:40:35][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-19 01:41:35][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 01:41:38][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ0suNxQYY/7qbqpmAgAMgkgU=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38377', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774148'}, 'type': 11041}
[DEBUG][2025-08-19 01:41:38][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:41:38][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:41:38][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:41:38][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:41:39][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:41:39][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ0suNxQYY/7qbqpmAgAMgkgU=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38377", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774148"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:41:39][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:41:39][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAAA0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:41:39][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAAA0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:41:39][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:41:39][godcmd.py:259] - [Godcmd] on_handle_context. content: 1
[DEBUG][2025-08-19 01:41:39][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:41:39][keyword.py:53] - [keyword] on_handle_context. content: 1
[DEBUG][2025-08-19 01:41:39][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:41:39][hello.py:97] - [Hello] on_handle_context. content: 1
[DEBUG][2025-08-19 01:41:39][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:41:39][finish.py:30] - [Finish] on_handle_context. content: 1
[DEBUG][2025-08-19 01:41:39][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-19 01:41:39][bridge.py:78] - create bot chatGPT for chat
[INFO][2025-08-19 01:41:40][chat_gpt_bot.py:60] - [CHATGPT] query=1
[DEBUG][2025-08-19 01:41:40][chat_gpt_session.py:77] - Warning: model not found. Using cl100k_base encoding.
[DEBUG][2025-08-19 01:41:43][chat_gpt_session.py:86] - num_tokens_from_messages() is not implemented for model mock. Returning num tokens assuming gpt-3.5-turbo.
[DEBUG][2025-08-19 01:41:43][session_manager.py:70] - prompt tokens used=14
[DEBUG][2025-08-19 01:41:43][chat_gpt_bot.py:77] - [CHATGPT] session query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '1'}]
[ERROR][2025-08-19 01:41:43][chat_gpt_bot.py:211] - [CHATGPT] Exception: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
Traceback (most recent call last):
  File "E:\Desktop\123\dify-on-wechat\bot\chatgpt\chat_gpt_bot.py", line 137, in reply_text
    response = openai.ChatCompletion.create(api_key=api_key, messages=session.messages, **args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\chat_completion.py", line 25, in create
    return super().create(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 149, in create
    ) = cls.__prepare_create_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 106, in __prepare_create_request
    requestor = api_requestor.APIRequestor(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_requestor.py", line 138, in __init__
    self.api_key = key or util.default_api_key()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\util.py", line 186, in default_api_key
    raise openai.error.AuthenticationError(
openai.error.AuthenticationError: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
[DEBUG][2025-08-19 01:41:43][chat_gpt_bot.py:90] - [CHATGPT] new_query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '1'}], session_id=S:****************_****************, reply_cont=我现在有点累了，等会再来吧, completion_tokens=0
[DEBUG][2025-08-19 01:41:43][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=ERROR, content=我现在有点累了，等会再来吧)
[DEBUG][2025-08-19 01:41:43][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAAA0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001AAA39D9120>})
[DEBUG][2025-08-19 01:41:43][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAAA0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001AAA39D9120>})
[INFO][2025-08-19 01:41:43][wework_channel.py:292] - [WX] sendMsg=Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), receiver=S:****************_****************
[DEBUG][2025-08-19 01:41:43][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:41:43][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ18uNxQYY76TDoYWAgAMgAQ==', 'at_list': [], 'content': '[ERROR]\n我现在有点累了，等会再来吧', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38378', 'receiver': '****************', 'send_time': '1755538903', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774151'}, 'type': 11041}
[DEBUG][2025-08-19 01:41:43][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:42:01][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ6cuNxQYY/7qbqpmAgAMglQU=', 'at_list': [], 'content': '我才', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38379', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774157'}, 'type': 11041}
[DEBUG][2025-08-19 01:42:01][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:42:01][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:42:01][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:42:01][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=我才, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:42:02][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:42:02][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ6cuNxQYY/7qbqpmAgAMglQU=", "at_list": [], "content": "我才", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38379", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774157"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=我才, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:42:02][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:42:02][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=我才, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DA560>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:42:02][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=我才, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DA560>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:42:02][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:02][godcmd.py:259] - [Godcmd] on_handle_context. content: 我才
[DEBUG][2025-08-19 01:42:02][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:02][keyword.py:53] - [keyword] on_handle_context. content: 我才
[DEBUG][2025-08-19 01:42:02][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:02][hello.py:97] - [Hello] on_handle_context. content: 我才
[DEBUG][2025-08-19 01:42:02][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:02][finish.py:30] - [Finish] on_handle_context. content: 我才
[DEBUG][2025-08-19 01:42:02][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=我才
[INFO][2025-08-19 01:42:02][chat_gpt_bot.py:60] - [CHATGPT] query=我才
[DEBUG][2025-08-19 01:42:02][chat_gpt_session.py:77] - Warning: model not found. Using cl100k_base encoding.
[DEBUG][2025-08-19 01:42:02][chat_gpt_session.py:86] - num_tokens_from_messages() is not implemented for model mock. Returning num tokens assuming gpt-3.5-turbo.
[DEBUG][2025-08-19 01:42:02][session_manager.py:70] - prompt tokens used=16
[DEBUG][2025-08-19 01:42:02][chat_gpt_bot.py:77] - [CHATGPT] session query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '我才'}]
[ERROR][2025-08-19 01:42:02][chat_gpt_bot.py:211] - [CHATGPT] Exception: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
Traceback (most recent call last):
  File "E:\Desktop\123\dify-on-wechat\bot\chatgpt\chat_gpt_bot.py", line 137, in reply_text
    response = openai.ChatCompletion.create(api_key=api_key, messages=session.messages, **args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\chat_completion.py", line 25, in create
    return super().create(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 149, in create
    ) = cls.__prepare_create_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 106, in __prepare_create_request
    requestor = api_requestor.APIRequestor(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_requestor.py", line 138, in __init__
    self.api_key = key or util.default_api_key()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\util.py", line 186, in default_api_key
    raise openai.error.AuthenticationError(
openai.error.AuthenticationError: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
[DEBUG][2025-08-19 01:42:02][chat_gpt_bot.py:90] - [CHATGPT] new_query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '我才'}], session_id=S:****************_****************, reply_cont=我现在有点累了，等会再来吧, completion_tokens=0
[DEBUG][2025-08-19 01:42:02][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=ERROR, content=我现在有点累了，等会再来吧)
[DEBUG][2025-08-19 01:42:02][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), context: Context(type=TEXT, content=我才, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DA560>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001AAA39D9120>})
[DEBUG][2025-08-19 01:42:02][wework_channel.py:276] - context: Context(type=TEXT, content=我才, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DA560>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001AAA39D9120>})
[INFO][2025-08-19 01:42:02][wework_channel.py:292] - [WX] sendMsg=Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), receiver=S:****************_****************
[DEBUG][2025-08-19 01:42:02][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:42:03][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ6suNxQYY76TDoYWAgAMgAg==', 'at_list': [], 'content': '[ERROR]\n我现在有点累了，等会再来吧', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38380', 'receiver': '****************', 'send_time': '1755538922', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774160'}, 'type': 11041}
[DEBUG][2025-08-19 01:42:03][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:42:20][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ/MuNxQYY/7qbqpmAgAMglwU=', 'at_list': [], 'content': '12316', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38381', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774166'}, 'type': 11041}
[DEBUG][2025-08-19 01:42:20][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:42:20][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:42:20][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:42:20][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=12316, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:42:22][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:42:22][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ/MuNxQYY/7qbqpmAgAMglwU=", "at_list": [], "content": "12316", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38381", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774166"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=12316, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:42:22][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:42:22][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=12316, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAF20>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:42:22][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=12316, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAF20>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:42:22][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:22][godcmd.py:259] - [Godcmd] on_handle_context. content: 12316
[DEBUG][2025-08-19 01:42:22][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:22][keyword.py:53] - [keyword] on_handle_context. content: 12316
[DEBUG][2025-08-19 01:42:22][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:22][hello.py:97] - [Hello] on_handle_context. content: 12316
[DEBUG][2025-08-19 01:42:22][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:42:22][finish.py:30] - [Finish] on_handle_context. content: 12316
[DEBUG][2025-08-19 01:42:22][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=12316
[INFO][2025-08-19 01:42:22][chat_gpt_bot.py:60] - [CHATGPT] query=12316
[DEBUG][2025-08-19 01:42:22][chat_gpt_session.py:77] - Warning: model not found. Using cl100k_base encoding.
[DEBUG][2025-08-19 01:42:22][chat_gpt_session.py:86] - num_tokens_from_messages() is not implemented for model mock. Returning num tokens assuming gpt-3.5-turbo.
[DEBUG][2025-08-19 01:42:22][session_manager.py:70] - prompt tokens used=15
[DEBUG][2025-08-19 01:42:22][chat_gpt_bot.py:77] - [CHATGPT] session query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '12316'}]
[ERROR][2025-08-19 01:42:22][chat_gpt_bot.py:211] - [CHATGPT] Exception: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
Traceback (most recent call last):
  File "E:\Desktop\123\dify-on-wechat\bot\chatgpt\chat_gpt_bot.py", line 137, in reply_text
    response = openai.ChatCompletion.create(api_key=api_key, messages=session.messages, **args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\chat_completion.py", line 25, in create
    return super().create(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 149, in create
    ) = cls.__prepare_create_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 106, in __prepare_create_request
    requestor = api_requestor.APIRequestor(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_requestor.py", line 138, in __init__
    self.api_key = key or util.default_api_key()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\util.py", line 186, in default_api_key
    raise openai.error.AuthenticationError(
openai.error.AuthenticationError: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
[DEBUG][2025-08-19 01:42:22][chat_gpt_bot.py:90] - [CHATGPT] new_query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '12316'}], session_id=S:****************_****************, reply_cont=我现在有点累了，等会再来吧, completion_tokens=0
[DEBUG][2025-08-19 01:42:22][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=ERROR, content=我现在有点累了，等会再来吧)
[DEBUG][2025-08-19 01:42:22][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), context: Context(type=TEXT, content=12316, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAF20>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001AAA39D9120>})
[DEBUG][2025-08-19 01:42:22][wework_channel.py:276] - context: Context(type=TEXT, content=12316, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001AAA65DAF20>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001AAA39D9120>})
[INFO][2025-08-19 01:42:22][wework_channel.py:292] - [WX] sendMsg=Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), receiver=S:****************_****************
[DEBUG][2025-08-19 01:42:22][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:42:22][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ/suNxQYY76TDoYWAgAMgBA==', 'at_list': [], 'content': '[ERROR]\n我现在有点累了，等会再来吧', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38382', 'receiver': '****************', 'send_time': '1755538942', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774173'}, 'type': 11041}
[DEBUG][2025-08-19 01:42:22][wework_channel.py:129] - 自己发的，直接结束
[INFO][2025-08-19 01:44:11][app.py:19] - signal 2 received, exiting...
[INFO][2025-08-19 01:44:11][config.py:275] - [Config] User datas saved.
[DEBUG][2025-08-19 01:44:48][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 01:44:48][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 01:44:48][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 01:44:48][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 01:44:48][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 01:44:48][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 01:44:48][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 01:44:48][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 01:44:48][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 01:44:48][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 01:44:48][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 01:44:48][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 01:44:48][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 01:44:48][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === hT1NO?c2n6wV ===。
[INFO][2025-08-19 01:44:48][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 01:44:48][keyword.py:35] - [keyword]加载配置文件E:\Desktop\123\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 01:44:48][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 01:44:48][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 01:44:48][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 01:44:48][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 01:44:48][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 01:44:48][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 01:44:48][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 01:44:48][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 01:44:48][hello.py:38] - [Hello] inited
[INFO][2025-08-19 01:44:48][finish.py:23] - [Finish] inited
[INFO][2025-08-19 01:44:49][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 01:44:50][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 01:44:50][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-19 01:45:10][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQm82NxQYY/7qbqpmAgAMgmQU=', 'at_list': [], 'content': '23', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38383', 'receiver': '****************', 'send_time': '1755539109', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774175'}, 'type': 11041}
[DEBUG][2025-08-19 01:45:28][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQuM2NxQYY/7qbqpmAgAMgnAU=', 'at_list': [], 'content': '12361', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38384', 'receiver': '****************', 'send_time': '1755539128', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774182'}, 'type': 11041}
[DEBUG][2025-08-19 01:48:21][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 01:48:21][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 01:48:21][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 01:48:21][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 01:48:21][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 01:48:22][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 01:48:22][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 01:48:22][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 01:48:22][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 01:48:22][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 01:48:22][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 01:48:22][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 01:48:22][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 01:48:22][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === 7%YAVpmA9KDk ===。
[INFO][2025-08-19 01:48:22][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 01:48:22][keyword.py:35] - [keyword]加载配置文件E:\Desktop\123\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 01:48:22][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 01:48:22][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 01:48:22][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 01:48:22][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 01:48:22][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 01:48:22][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 01:48:22][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 01:48:22][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 01:48:22][hello.py:38] - [Hello] inited
[INFO][2025-08-19 01:48:22][finish.py:23] - [Finish] inited
[INFO][2025-08-19 01:48:23][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 01:48:24][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 01:48:24][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-19 01:48:31][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ786NxQYY/7qbqpmAgAMgnQU=', 'at_list': [], 'content': '111', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38385', 'receiver': '****************', 'send_time': '1755539310', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774189'}, 'type': 11041}
[INFO][2025-08-19 01:49:24][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 01:49:45][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQuc+NxQYY/7qbqpmAgAMgngU=', 'at_list': [], 'content': '********', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38386', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774196'}, 'type': 11041}
[DEBUG][2025-08-19 01:49:45][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:49:45][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:49:45][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:49:45][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=********, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:49:46][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:49:46][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQuc+NxQYY/7qbqpmAgAMgngU=", "at_list": [], "content": "********", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38386", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774196"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=********, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:49:46][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:49:46][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=********, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CAC80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:49:46][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=********, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CAC80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:49:46][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:46][godcmd.py:259] - [Godcmd] on_handle_context. content: ********
[DEBUG][2025-08-19 01:49:46][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:46][keyword.py:53] - [keyword] on_handle_context. content: ********
[DEBUG][2025-08-19 01:49:46][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:46][hello.py:97] - [Hello] on_handle_context. content: ********
[DEBUG][2025-08-19 01:49:46][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:46][finish.py:30] - [Finish] on_handle_context. content: ********
[DEBUG][2025-08-19 01:49:46][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=********
[INFO][2025-08-19 01:49:46][bridge.py:78] - create bot chatGPT for chat
[INFO][2025-08-19 01:49:46][chat_gpt_bot.py:60] - [CHATGPT] query=********
[DEBUG][2025-08-19 01:49:46][chat_gpt_session.py:77] - Warning: model not found. Using cl100k_base encoding.
[DEBUG][2025-08-19 01:49:47][chat_gpt_session.py:86] - num_tokens_from_messages() is not implemented for model mock. Returning num tokens assuming gpt-3.5-turbo.
[DEBUG][2025-08-19 01:49:47][session_manager.py:70] - prompt tokens used=16
[DEBUG][2025-08-19 01:49:47][chat_gpt_bot.py:77] - [CHATGPT] session query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '********'}]
[ERROR][2025-08-19 01:49:47][chat_gpt_bot.py:211] - [CHATGPT] Exception: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
Traceback (most recent call last):
  File "E:\Desktop\123\dify-on-wechat\bot\chatgpt\chat_gpt_bot.py", line 137, in reply_text
    response = openai.ChatCompletion.create(api_key=api_key, messages=session.messages, **args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\chat_completion.py", line 25, in create
    return super().create(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 149, in create
    ) = cls.__prepare_create_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 106, in __prepare_create_request
    requestor = api_requestor.APIRequestor(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_requestor.py", line 138, in __init__
    self.api_key = key or util.default_api_key()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\util.py", line 186, in default_api_key
    raise openai.error.AuthenticationError(
openai.error.AuthenticationError: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
[DEBUG][2025-08-19 01:49:47][chat_gpt_bot.py:90] - [CHATGPT] new_query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '********'}], session_id=S:****************_****************, reply_cont=我现在有点累了，等会再来吧, completion_tokens=0
[DEBUG][2025-08-19 01:49:47][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=ERROR, content=我现在有点累了，等会再来吧)
[DEBUG][2025-08-19 01:49:47][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), context: Context(type=TEXT, content=********, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CAC80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001C1988F9120>})
[DEBUG][2025-08-19 01:49:47][wework_channel.py:276] - context: Context(type=TEXT, content=********, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CAC80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001C1988F9120>})
[INFO][2025-08-19 01:49:47][wework_channel.py:292] - [WX] sendMsg=Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), receiver=S:****************_****************
[DEBUG][2025-08-19 01:49:47][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:49:47][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQu8+NxQYY76TDoYWAgAMgCQ==', 'at_list': [], 'content': '[ERROR]\n我现在有点累了，等会再来吧', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38387', 'receiver': '****************', 'send_time': '1755539387', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774203'}, 'type': 11041}
[DEBUG][2025-08-19 01:49:47][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:49:55][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQws+NxQYY/7qbqpmAgAMgoAU=', 'at_list': [], 'content': '2345453', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38388', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774207'}, 'type': 11041}
[DEBUG][2025-08-19 01:49:55][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:49:55][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:49:55][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:49:55][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=2345453, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:49:57][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:49:57][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQws+NxQYY/7qbqpmAgAMgoAU=", "at_list": [], "content": "2345453", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38388", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774207"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=2345453, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:49:57][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:49:57][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=2345453, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CA9E0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:49:57][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=2345453, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CA9E0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:49:57][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:57][godcmd.py:259] - [Godcmd] on_handle_context. content: 2345453
[DEBUG][2025-08-19 01:49:57][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:57][keyword.py:53] - [keyword] on_handle_context. content: 2345453
[DEBUG][2025-08-19 01:49:57][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:57][hello.py:97] - [Hello] on_handle_context. content: 2345453
[DEBUG][2025-08-19 01:49:57][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:49:57][finish.py:30] - [Finish] on_handle_context. content: 2345453
[DEBUG][2025-08-19 01:49:57][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=2345453
[INFO][2025-08-19 01:49:57][chat_gpt_bot.py:60] - [CHATGPT] query=2345453
[DEBUG][2025-08-19 01:49:57][chat_gpt_session.py:77] - Warning: model not found. Using cl100k_base encoding.
[DEBUG][2025-08-19 01:49:57][chat_gpt_session.py:86] - num_tokens_from_messages() is not implemented for model mock. Returning num tokens assuming gpt-3.5-turbo.
[DEBUG][2025-08-19 01:49:57][session_manager.py:70] - prompt tokens used=16
[DEBUG][2025-08-19 01:49:57][chat_gpt_bot.py:77] - [CHATGPT] session query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '2345453'}]
[ERROR][2025-08-19 01:49:57][chat_gpt_bot.py:211] - [CHATGPT] Exception: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
Traceback (most recent call last):
  File "E:\Desktop\123\dify-on-wechat\bot\chatgpt\chat_gpt_bot.py", line 137, in reply_text
    response = openai.ChatCompletion.create(api_key=api_key, messages=session.messages, **args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\chat_completion.py", line 25, in create
    return super().create(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 149, in create
    ) = cls.__prepare_create_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 106, in __prepare_create_request
    requestor = api_requestor.APIRequestor(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_requestor.py", line 138, in __init__
    self.api_key = key or util.default_api_key()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\util.py", line 186, in default_api_key
    raise openai.error.AuthenticationError(
openai.error.AuthenticationError: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
[DEBUG][2025-08-19 01:49:57][chat_gpt_bot.py:90] - [CHATGPT] new_query=[{'role': 'system', 'content': ''}, {'role': 'user', 'content': '2345453'}], session_id=S:****************_****************, reply_cont=我现在有点累了，等会再来吧, completion_tokens=0
[DEBUG][2025-08-19 01:49:57][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=ERROR, content=我现在有点累了，等会再来吧)
[DEBUG][2025-08-19 01:49:57][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), context: Context(type=TEXT, content=2345453, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CA9E0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001C1988F9120>})
[DEBUG][2025-08-19 01:49:57][wework_channel.py:276] - context: Context(type=TEXT, content=2345453, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001C19B4CA9E0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001C1988F9120>})
[INFO][2025-08-19 01:49:57][wework_channel.py:292] - [WX] sendMsg=Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), receiver=S:****************_****************
[DEBUG][2025-08-19 01:49:57][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:49:57][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQxc+NxQYY76TDoYWAgAMgCw==', 'at_list': [], 'content': '[ERROR]\n我现在有点累了，等会再来吧', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38389', 'receiver': '****************', 'send_time': '1755539397', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774214'}, 'type': 11041}
[DEBUG][2025-08-19 01:49:57][wework_channel.py:129] - 自己发的，直接结束
[INFO][2025-08-19 01:50:32][app.py:19] - signal 2 received, exiting...
[INFO][2025-08-19 01:50:32][config.py:275] - [Config] User datas saved.
[DEBUG][2025-08-19 01:50:39][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 01:50:39][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 01:50:39][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 01:50:40][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 01:50:40][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 01:50:40][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 01:50:40][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 01:50:40][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 01:50:40][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 01:50:40][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 01:50:40][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 01:50:40][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 01:50:40][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 01:50:40][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === #6D1FYjl%?YY ===。
[INFO][2025-08-19 01:50:40][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 01:50:40][keyword.py:35] - [keyword]加载配置文件E:\Desktop\123\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 01:50:40][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 01:50:40][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 01:50:40][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 01:50:40][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 01:50:40][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 01:50:40][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 01:50:40][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 01:50:40][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 01:50:40][hello.py:38] - [Hello] inited
[INFO][2025-08-19 01:50:40][finish.py:23] - [Finish] inited
[INFO][2025-08-19 01:50:40][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 01:50:41][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 01:50:41][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-19 01:50:52][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ+8+NxQYY/7qbqpmAgAMgogU=', 'at_list': [], 'content': '4444', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38390', 'receiver': '****************', 'send_time': '1755539451', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774218'}, 'type': 11041}
[DEBUG][2025-08-19 01:51:03][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQh9CNxQYY/7qbqpmAgAMgowU=', 'at_list': [], 'content': '555', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38391', 'receiver': '****************', 'send_time': '1755539463', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774225'}, 'type': 11041}
[DEBUG][2025-08-19 01:51:30][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQodCNxQYY/7qbqpmAgAMgpAU=', 'at_list': [], 'content': '66', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38392', 'receiver': '****************', 'send_time': '1755539489', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774232'}, 'type': 11041}
[INFO][2025-08-19 01:51:41][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 01:51:57][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQvNCNxQYY/7qbqpmAgAMgpQU=', 'at_list': [], 'content': '77', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38393', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774239'}, 'type': 11041}
[DEBUG][2025-08-19 01:51:57][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:51:57][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:51:57][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:51:57][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=77, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:51:59][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:51:59][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQvNCNxQYY/7qbqpmAgAMgpQU=", "at_list": [], "content": "77", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38393", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774239"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=77, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:51:59][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:51:59][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=77, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BF0D0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:51:59][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=77, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BF0D0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:51:59][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:51:59][godcmd.py:259] - [Godcmd] on_handle_context. content: 77
[DEBUG][2025-08-19 01:51:59][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:51:59][keyword.py:53] - [keyword] on_handle_context. content: 77
[DEBUG][2025-08-19 01:51:59][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:51:59][hello.py:97] - [Hello] on_handle_context. content: 77
[DEBUG][2025-08-19 01:51:59][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:51:59][finish.py:30] - [Finish] on_handle_context. content: 77
[DEBUG][2025-08-19 01:51:59][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=77
[INFO][2025-08-19 01:51:59][bridge.py:80] - create bot mock for chat
[INFO][2025-08-19 01:51:59][mock_bot.py:21] - [MockBot] 收到消息: 77
[INFO][2025-08-19 01:51:59][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 01:51:59][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 01:51:59][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=77, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BF0D0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001E7CF86F640>})
[DEBUG][2025-08-19 01:51:59][wework_channel.py:276] - context: Context(type=TEXT, content=77, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BF0D0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001E7CF86F640>})
[DEBUG][2025-08-19 01:51:59][wework_channel.py:281] - match: None
[INFO][2025-08-19 01:51:59][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-19 01:51:59][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:51:59][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQv9CNxQYY76TDoYWAgAMgEA==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38394', 'receiver': '****************', 'send_time': '1755539519', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774246'}, 'type': 11041}
[DEBUG][2025-08-19 01:51:59][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:52:10][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQytCNxQYY/7qbqpmAgAMgpgU=', 'at_list': [], 'content': '999', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38395', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774248'}, 'type': 11041}
[DEBUG][2025-08-19 01:52:10][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:52:10][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:52:10][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:52:10][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=999, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:52:11][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:52:11][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQytCNxQYY/7qbqpmAgAMgpgU=", "at_list": [], "content": "999", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38395", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774248"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=999, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:52:11][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:52:11][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=999, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BEE30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:52:11][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=999, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BEE30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:52:11][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:52:11][godcmd.py:259] - [Godcmd] on_handle_context. content: 999
[DEBUG][2025-08-19 01:52:11][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:52:11][keyword.py:53] - [keyword] on_handle_context. content: 999
[DEBUG][2025-08-19 01:52:11][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:52:11][hello.py:97] - [Hello] on_handle_context. content: 999
[DEBUG][2025-08-19 01:52:11][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:52:11][finish.py:30] - [Finish] on_handle_context. content: 999
[DEBUG][2025-08-19 01:52:11][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=999
[INFO][2025-08-19 01:52:11][mock_bot.py:21] - [MockBot] 收到消息: 999
[INFO][2025-08-19 01:52:11][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 01:52:11][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 01:52:11][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=999, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BEE30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001E7CF86F640>})
[DEBUG][2025-08-19 01:52:11][wework_channel.py:276] - context: Context(type=TEXT, content=999, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001E7D24BEE30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001E7CF86F640>})
[DEBUG][2025-08-19 01:52:11][wework_channel.py:281] - match: None
[INFO][2025-08-19 01:52:11][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-19 01:52:11][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:52:11][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQy9CNxQYY76TDoYWAgAMgEg==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38396', 'receiver': '****************', 'send_time': '1755539531', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774255'}, 'type': 11041}
[DEBUG][2025-08-19 01:52:11][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:52:26][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAMQ2tCNxQYY76TDoYWAgAMg0tP2ugQ=', 'at_list': [], 'content': '11', 'content_type': 0, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38397', 'receiver': '****************', 'send_time': '1755539546', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774261'}, 'type': 11041}
[DEBUG][2025-08-19 01:52:26][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:53:22][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 01:53:22][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 01:53:22][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 01:53:24][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 01:53:24][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 01:53:24][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 01:53:24][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 01:53:24][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 01:53:24][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 01:53:24][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 01:53:24][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 01:53:24][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 01:53:24][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 01:53:24][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === 1wEOz$Wl3gmC ===。
[INFO][2025-08-19 01:53:24][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 01:53:24][keyword.py:35] - [keyword]加载配置文件E:\Desktop\123\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 01:53:24][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 01:53:24][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 01:53:24][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 01:53:24][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 01:53:24][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 01:53:24][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 01:53:24][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 01:53:24][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 01:53:24][hello.py:38] - [Hello] inited
[INFO][2025-08-19 01:53:25][finish.py:23] - [Finish] inited
[INFO][2025-08-19 01:53:26][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 01:53:27][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 01:53:27][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-19 01:53:41][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQpdGNxQYY/7qbqpmAgAMgqAU=', 'at_list': [], 'content': '4444', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38398', 'receiver': '****************', 'send_time': '1755539621', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774267'}, 'type': 11041}
[INFO][2025-08-19 01:54:27][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 01:54:38][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ3tGNxQYY/7qbqpmAgAMgqgU=', 'at_list': [], 'content': '66', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38399', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774272'}, 'type': 11041}
[DEBUG][2025-08-19 01:54:38][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:54:38][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:54:39][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:54:39][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=66, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:54:40][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:54:40][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ3tGNxQYY/7qbqpmAgAMgqgU=", "at_list": [], "content": "66", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38399", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1774272"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=66, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:54:40][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:54:40][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=66, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16A380>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:54:40][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=66, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16A380>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:54:40][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:54:40][godcmd.py:259] - [Godcmd] on_handle_context. content: 66
[DEBUG][2025-08-19 01:54:40][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:54:40][keyword.py:53] - [keyword] on_handle_context. content: 66
[DEBUG][2025-08-19 01:54:40][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:54:40][hello.py:97] - [Hello] on_handle_context. content: 66
[DEBUG][2025-08-19 01:54:40][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:54:40][finish.py:30] - [Finish] on_handle_context. content: 66
[DEBUG][2025-08-19 01:54:40][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=66
[INFO][2025-08-19 01:54:40][bridge.py:80] - create bot mock for chat
[INFO][2025-08-19 01:54:40][mock_bot.py:21] - [MockBot] 收到消息: 66
[INFO][2025-08-19 01:54:40][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 01:54:40][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 01:54:41][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=66, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16A380>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000024C0CA797B0>})
[DEBUG][2025-08-19 01:54:41][wework_channel.py:276] - context: Context(type=TEXT, content=66, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16A380>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000024C0CA797B0>})
[DEBUG][2025-08-19 01:54:41][wework_channel.py:281] - match: None
[INFO][2025-08-19 01:54:41][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-19 01:54:41][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:54:41][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ4dGNxQYY76TDoYWAgAMgFQ==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38400', 'receiver': '****************', 'send_time': '1755539681', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774277'}, 'type': 11041}
[DEBUG][2025-08-19 01:54:41][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:55:17][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAMQhNKNxQYYyNeng42AgAMgxKLvEA==', 'at_list': [], 'content': '46526', 'content_type': 0, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38401', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '王海福', 'server_id': '1774279'}, 'type': 11041}
[DEBUG][2025-08-19 01:55:17][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:55:17][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:55:17][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:55:17][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=46526, from_user_id=S:****************_****************, from_user_nickname=王海福, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=王海福, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:55:19][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:55:19][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAMQhNKNxQYYyNeng42AgAMgxKLvEA==", "at_list": [], "content": "46526", "content_type": 0, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38401", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "王海福", "server_id": "1774279"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=46526, from_user_id=S:****************_****************, from_user_nickname=王海福, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=王海福, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:55:19][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:55:19][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=46526, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16B190>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:55:19][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=46526, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16B190>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:55:19][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:19][godcmd.py:259] - [Godcmd] on_handle_context. content: 46526
[DEBUG][2025-08-19 01:55:19][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:19][keyword.py:53] - [keyword] on_handle_context. content: 46526
[DEBUG][2025-08-19 01:55:20][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:20][hello.py:97] - [Hello] on_handle_context. content: 46526
[DEBUG][2025-08-19 01:55:20][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:20][finish.py:30] - [Finish] on_handle_context. content: 46526
[DEBUG][2025-08-19 01:55:20][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=46526
[INFO][2025-08-19 01:55:20][mock_bot.py:21] - [MockBot] 收到消息: 46526
[INFO][2025-08-19 01:55:20][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 01:55:20][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 01:55:20][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=46526, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16B190>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000024C0CA797B0>})
[DEBUG][2025-08-19 01:55:20][wework_channel.py:276] - context: Context(type=TEXT, content=46526, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16B190>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000024C0CA797B0>})
[DEBUG][2025-08-19 01:55:20][wework_channel.py:281] - match: None
[INFO][2025-08-19 01:55:20][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-19 01:55:20][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:55:20][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQiNKNxQYY76TDoYWAgAMgFg==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38402', 'receiver': '****************', 'send_time': '1755539720', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774282'}, 'type': 11041}
[DEBUG][2025-08-19 01:55:20][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 01:55:30][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAMQktKNxQYYyNeng42AgAMgq4DGDw==', 'at_list': [], 'content': '你吃饭了没', 'content_type': 0, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38403', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '王海福', 'server_id': '1774286'}, 'type': 11041}
[DEBUG][2025-08-19 01:55:30][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 01:55:30][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 13240, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 01:55:30][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 01:55:30][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=你吃饭了没, from_user_id=S:****************_****************, from_user_nickname=王海福, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=王海福, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:55:31][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 01:55:31][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAMQktKNxQYYyNeng42AgAMgq4DGDw==", "at_list": [], "content": "你吃饭了没", "content_type": 0, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38403", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "王海福", "server_id": "1774286"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=你吃饭了没, from_user_id=S:****************_****************, from_user_nickname=王海福, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=王海福, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 01:55:31][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 01:55:32][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=你吃饭了没, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16AB30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:55:32][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=你吃饭了没, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16AB30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 01:55:32][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:32][godcmd.py:259] - [Godcmd] on_handle_context. content: 你吃饭了没
[DEBUG][2025-08-19 01:55:32][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:32][keyword.py:53] - [keyword] on_handle_context. content: 你吃饭了没
[DEBUG][2025-08-19 01:55:32][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:32][hello.py:97] - [Hello] on_handle_context. content: 你吃饭了没
[DEBUG][2025-08-19 01:55:32][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 01:55:32][finish.py:30] - [Finish] on_handle_context. content: 你吃饭了没
[DEBUG][2025-08-19 01:55:32][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=你吃饭了没
[INFO][2025-08-19 01:55:32][mock_bot.py:21] - [MockBot] 收到消息: 你吃饭了没
[INFO][2025-08-19 01:55:32][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 01:55:32][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 01:55:32][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=你吃饭了没, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16AB30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000024C0CA797B0>})
[DEBUG][2025-08-19 01:55:32][wework_channel.py:276] - context: Context(type=TEXT, content=你吃饭了没, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000024C0E16AB30>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000024C0CA797B0>})
[DEBUG][2025-08-19 01:55:32][wework_channel.py:281] - match: None
[INFO][2025-08-19 01:55:32][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-19 01:55:32][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 01:55:32][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQlNKNxQYY76TDoYWAgAMgFw==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38404', 'receiver': '****************', 'send_time': '1755539732', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774289'}, 'type': 11041}
[DEBUG][2025-08-19 01:55:33][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 05:01:32][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 05:01:32][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 05:01:32][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 05:01:32][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 05:01:32][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 05:01:32][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 05:01:32][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 05:01:32][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 05:01:32][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 05:01:32][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 05:01:32][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 05:01:32][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 05:01:32][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 05:01:32][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === UnacFI&$83PI ===。
[INFO][2025-08-19 05:01:32][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 05:01:32][keyword.py:35] - [keyword]加载配置文件E:\Desktop\123 - 副本\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 05:01:32][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 05:01:32][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 05:01:33][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 05:01:33][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 05:01:33][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 05:01:33][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 05:01:33][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 05:01:33][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 05:01:33][hello.py:38] - [Hello] inited
[INFO][2025-08-19 05:01:33][finish.py:23] - [Finish] inited
[INFO][2025-08-19 05:01:34][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 05:01:35][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 05:01:35][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-19 05:02:35][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 05:02:52][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBDyqY7FBhj/upuqmYCAAyDOBQ==', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38443', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774497'}, 'type': 11041}
[DEBUG][2025-08-19 05:02:52][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-19 05:02:52][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18992, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 05:02:52][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-19 05:02:52][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'wework_****************_**********_887819_8328285670040534810', 'at_list': [], 'content': '20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 0, 'local_id': '38444', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '', 'server_id': '1774499'}, 'type': 11041}
[DEBUG][2025-08-19 05:02:52][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 05:02:52][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18992, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 05:02:52][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: O:****************
[DEBUG][2025-08-19 05:02:52][wework_channel.py:95] - cmsg:ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 05:02:53][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-19 05:02:53][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 05:02:54][wework_message.py:203] - at_list: []
[DEBUG][2025-08-19 05:02:54][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "wework_****************_**********_887819_8328285670040534810", "at_list": [], "content": "20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81", "content_type": 2, "conversation_id": "O:****************", "is_pc": 0, "local_id": "38444", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "", "server_id": "1774499"}, "type": 11041}, cmsg=ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 05:02:54][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-19 05:02:54][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 05:02:54][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B836A830>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 05:02:54][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-19 05:02:54][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B836A830>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 05:02:54][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=1, from_user_id=R:***************, from_user_nickname=SSH推送通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SSH推送通知, is_group=True, is_at=False, actual_user_id=****************, actual_user_nickname=尚二松, at_list=[]
[DEBUG][2025-08-19 05:02:55][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:02:55][godcmd.py:259] - [Godcmd] on_handle_context. content: 20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81
[DEBUG][2025-08-19 05:02:55][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:02:55][keyword.py:53] - [keyword] on_handle_context. content: 20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81
[DEBUG][2025-08-19 05:02:55][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:02:55][hello.py:97] - [Hello] on_handle_context. content: 20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81
[DEBUG][2025-08-19 05:02:55][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:02:55][finish.py:30] - [Finish] on_handle_context. content: 20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81
[DEBUG][2025-08-19 05:02:55][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81
[INFO][2025-08-19 05:02:55][bridge.py:80] - create bot mock for chat
[INFO][2025-08-19 05:02:55][mock_bot.py:21] - [MockBot] 收到消息: 20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81
[INFO][2025-08-19 05:02:55][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 05:02:55][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 05:02:55][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B836A830>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000199B6C7D7B0>})
[DEBUG][2025-08-19 05:02:55][wework_channel.py:276] - context: Context(type=TEXT, content=20:00----05:00  CELL <br>A5335:  GPO-HHC6B:  93.81, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B836A830>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000199B6C7D7B0>})
[DEBUG][2025-08-19 05:02:55][wework_channel.py:281] - match: None
[INFO][2025-08-19 05:02:55][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=O:****************
[DEBUG][2025-08-19 05:02:55][chat_channel.py:323] - Worker return success, session_id = O:****************
[DEBUG][2025-08-19 05:02:55][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGAGBD/qY7FBhjvpMOhhYCAAyAG', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 1, 'local_id': '38445', 'receiver': 'O:****************', 'send_time': '1755550975', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774501'}, 'type': 11041}
[DEBUG][2025-08-19 05:02:56][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 05:02:56][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[DEBUG][2025-08-19 05:02:56][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-19 05:03:29][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBCgqo7FBhj/upuqmYCAAyDPBQ==', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38446', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774512'}, 'type': 11041}
[DEBUG][2025-08-19 05:03:29][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-19 05:03:29][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18992, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 05:03:29][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-19 05:03:30][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-19 05:03:30][wework_message.py:203] - at_list: []
[DEBUG][2025-08-19 05:03:31][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-19 05:03:31][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-19 05:03:31][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=1, from_user_id=R:***************, from_user_nickname=SSH推送通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SSH推送通知, is_group=True, is_at=False, actual_user_id=****************, actual_user_nickname=尚二松, at_list=[]
[DEBUG][2025-08-19 05:03:33][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[DEBUG][2025-08-19 05:03:33][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-19 05:03:37][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'wework_****************_**********_327795_13225916740444273486', 'at_list': [], 'content': '20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 0, 'local_id': '38447', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '', 'server_id': '1774514'}, 'type': 11041}
[DEBUG][2025-08-19 05:03:37][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 05:03:37][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18992, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 05:03:38][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: O:****************
[DEBUG][2025-08-19 05:03:38][wework_channel.py:95] - cmsg:ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 05:03:39][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 05:03:40][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "wework_****************_**********_327795_13225916740444273486", "at_list": [], "content": "20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17", "content_type": 2, "conversation_id": "O:****************", "is_pc": 0, "local_id": "38447", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "", "server_id": "1774514"}, "type": 11041}, cmsg=ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 05:03:40][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 05:03:40][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B8369F60>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 05:03:40][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B8369F60>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 05:03:40][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:03:40][godcmd.py:259] - [Godcmd] on_handle_context. content: 20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17
[DEBUG][2025-08-19 05:03:40][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:03:41][keyword.py:53] - [keyword] on_handle_context. content: 20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17
[DEBUG][2025-08-19 05:03:41][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:03:41][hello.py:97] - [Hello] on_handle_context. content: 20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17
[DEBUG][2025-08-19 05:03:41][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:03:41][finish.py:30] - [Finish] on_handle_context. content: 20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17
[DEBUG][2025-08-19 05:03:41][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17
[INFO][2025-08-19 05:03:41][mock_bot.py:21] - [MockBot] 收到消息: 20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17
[INFO][2025-08-19 05:03:41][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 05:03:41][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 05:03:42][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B8369F60>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000199B6C7D7B0>})
[DEBUG][2025-08-19 05:03:42][wework_channel.py:276] - context: Context(type=TEXT, content=20:00----05:00  FT <br>A5335:  GPO-HHC6B:  99.17, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000199B8369F60>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000199B6C7D7B0>})
[DEBUG][2025-08-19 05:03:42][wework_channel.py:281] - match: None
[INFO][2025-08-19 05:03:42][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=O:****************
[DEBUG][2025-08-19 05:03:42][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGAGBCuqo7FBhjvpMOhhYCAAyAK', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 1, 'local_id': '38448', 'receiver': 'O:****************', 'send_time': '1755551022', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774516'}, 'type': 11041}
[DEBUG][2025-08-19 05:03:42][chat_channel.py:323] - Worker return success, session_id = O:****************
[DEBUG][2025-08-19 05:03:42][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 05:03:58][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBC+qo7FBhj/upuqmYCAAyDQBQ==', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38449', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774526'}, 'type': 11041}
[DEBUG][2025-08-19 05:03:58][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-19 05:03:58][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18992, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 05:03:58][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-19 05:04:00][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-19 05:04:00][wework_message.py:203] - at_list: []
[DEBUG][2025-08-19 05:04:00][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-19 05:04:00][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-19 05:04:00][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=1, from_user_id=R:***************, from_user_nickname=SSH推送通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SSH推送通知, is_group=True, is_at=False, actual_user_id=****************, actual_user_nickname=尚二松, at_list=[]
[DEBUG][2025-08-19 05:04:03][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[DEBUG][2025-08-19 05:04:03][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-19 05:09:48][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 05:09:48][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 05:09:48][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 05:09:48][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 05:09:48][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 05:09:48][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 05:09:48][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 05:09:48][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 05:09:48][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 05:09:48][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 05:09:48][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 05:09:48][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 05:09:49][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 05:09:49][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === uUPVFc5&0D?D ===。
[INFO][2025-08-19 05:09:49][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 05:09:49][keyword.py:35] - [keyword]加载配置文件E:\Desktop\123 - 副本\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 05:09:49][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 05:09:49][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 05:09:49][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 05:09:49][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 05:09:49][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 05:09:49][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 05:09:49][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 05:09:49][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 05:09:49][hello.py:38] - [Hello] inited
[INFO][2025-08-19 05:09:49][finish.py:23] - [Finish] inited
[INFO][2025-08-19 05:09:50][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 05:09:51][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 05:09:51][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-19 05:10:51][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 05:11:52][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBCXro7FBhj/upuqmYCAAyDRBQ==', 'at_list': [{'nickname': '童树运', 'user_id': '****************'}], 'content': '@童树运  123', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38450', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1774555'}, 'type': 11041}
[DEBUG][2025-08-19 05:11:52][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-19 05:11:52][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18992, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 05:11:52][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-19 05:11:53][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-19 05:11:53][wework_message.py:203] - at_list: ['童树运']
[DEBUG][2025-08-19 05:11:53][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-19 05:11:54][wework_message.py:215] - Wechaty message R:*************** includes at
[DEBUG][2025-08-19 05:11:54][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-19 05:11:54][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=@童树运  123, from_user_id=R:***************, from_user_nickname=SSH推送通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SSH推送通知, is_group=True, is_at=True, actual_user_id=****************, actual_user_nickname=尚二松, at_list=['童树运']
[DEBUG][2025-08-19 05:11:55][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[INFO][2025-08-19 05:11:55][chat_channel.py:117] - [chat_channel]receive group at
[DEBUG][2025-08-19 05:11:55][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-19 05:11:55][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=123, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001D3A309A140>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************'})
[DEBUG][2025-08-19 05:11:56][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=123, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001D3A309A140>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************'})
[DEBUG][2025-08-19 05:11:56][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:11:56][godcmd.py:259] - [Godcmd] on_handle_context. content: 123
[DEBUG][2025-08-19 05:11:56][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:11:56][keyword.py:53] - [keyword] on_handle_context. content: 123
[DEBUG][2025-08-19 05:11:56][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:11:56][hello.py:97] - [Hello] on_handle_context. content: 123
[DEBUG][2025-08-19 05:11:56][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 05:11:56][finish.py:30] - [Finish] on_handle_context. content: 123
[DEBUG][2025-08-19 05:11:56][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=123
[INFO][2025-08-19 05:11:57][bridge.py:80] - create bot mock for chat
[INFO][2025-08-19 05:11:57][mock_bot.py:21] - [MockBot] 收到消息: 123
[INFO][2025-08-19 05:11:57][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 05:11:57][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 05:11:57][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=@尚二松
我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=123, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001D3A309A140>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001D3A19A97B0>})
[DEBUG][2025-08-19 05:11:57][wework_channel.py:276] - context: Context(type=TEXT, content=123, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001D3A309A140>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001D3A19A97B0>})
[DEBUG][2025-08-19 05:11:57][wework_channel.py:281] - match: <re.Match object; span=(0, 5), match='@尚二松\n'>
[DEBUG][2025-08-19 05:11:57][wework_channel.py:285] - new_content: 
我已经下班，有问题明天再说，急事请电联
[INFO][2025-08-19 05:11:57][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=@尚二松
我已经下班，有问题明天再说，急事请电联), receiver=R:***************
[DEBUG][2025-08-19 05:11:57][chat_channel.py:323] - Worker return success, session_id = ****************@@R:***************
[DEBUG][2025-08-19 05:11:57][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBCdro7FBhjvpMOhhYCAAyAR', 'at_list': [{'nickname': '尚二松', 'user_id': '****************'}], 'content': '@尚二松 \n我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 1, 'local_id': '38451', 'receiver': 'R:***************', 'send_time': '1755551517', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1774558'}, 'type': 11041}
[DEBUG][2025-08-19 05:11:57][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 07:40:48][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 07:40:48][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 07:40:48][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 07:40:48][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 07:40:48][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 07:40:48][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 07:40:48][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 07:40:48][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 07:40:48][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 07:40:48][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 07:40:48][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 07:40:48][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 07:40:48][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 07:40:48][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === L?v7?DhDV3lr ===。
[INFO][2025-08-19 07:40:48][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 07:40:48][keyword.py:35] - [keyword]加载配置文件E:\Desktop\45\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 07:40:48][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 07:40:48][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 07:40:48][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 07:40:48][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 07:40:48][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 07:40:48][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 07:40:49][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 07:40:49][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 07:40:49][hello.py:38] - [Hello] inited
[INFO][2025-08-19 07:40:49][finish.py:23] - [Finish] inited
[INFO][2025-08-19 07:40:50][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 07:40:51][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 07:40:51][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[ERROR][2025-08-19 07:44:28][config.py:302] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\config.py", line 286, in drag_sensitive
    conf_dict: dict = json.loads(config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:44:28][app.py:66] - App startup failed!
[ERROR][2025-08-19 07:44:28][app.py:67] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\app.py", line 46, in run
    load_config()
  File "E:\Desktop\45\dify-on-wechat\config.py", line 318, in load_config
    config = Config(json.loads(config_str))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:44:39][config.py:302] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\config.py", line 286, in drag_sensitive
    conf_dict: dict = json.loads(config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:44:39][app.py:66] - App startup failed!
[ERROR][2025-08-19 07:44:39][app.py:67] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\app.py", line 46, in run
    load_config()
  File "E:\Desktop\45\dify-on-wechat\config.py", line 318, in load_config
    config = Config(json.loads(config_str))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:45:39][config.py:302] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\config.py", line 286, in drag_sensitive
    conf_dict: dict = json.loads(config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:45:39][app.py:66] - App startup failed!
[ERROR][2025-08-19 07:45:39][app.py:67] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\app.py", line 46, in run
    load_config()
  File "E:\Desktop\45\dify-on-wechat\config.py", line 318, in load_config
    config = Config(json.loads(config_str))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[DEBUG][2025-08-19 07:46:06][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 07:46:06][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 07:46:06][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 07:46:06][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 07:46:06][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 07:46:06][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 07:46:06][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 07:46:06][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 07:46:06][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 07:46:06][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 07:46:06][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 07:46:06][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 07:46:06][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 07:46:06][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === s1EvBWBY?caU ===。
[INFO][2025-08-19 07:46:07][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 07:46:07][keyword.py:35] - [keyword]加载配置文件E:\Desktop\45\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 07:46:07][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 07:46:07][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 07:46:07][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 07:46:07][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 07:46:07][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 07:46:07][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 07:46:07][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 07:46:07][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 07:46:07][hello.py:38] - [Hello] inited
[INFO][2025-08-19 07:46:07][finish.py:23] - [Finish] inited
[INFO][2025-08-19 07:46:08][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 07:46:09][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 07:46:09][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[ERROR][2025-08-19 07:46:46][config.py:302] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\config.py", line 286, in drag_sensitive
    conf_dict: dict = json.loads(config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:46:46][app.py:66] - App startup failed!
[ERROR][2025-08-19 07:46:46][app.py:67] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\app.py", line 46, in run
    load_config()
  File "E:\Desktop\45\dify-on-wechat\config.py", line 318, in load_config
    config = Config(json.loads(config_str))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:47:58][config.py:302] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\config.py", line 286, in drag_sensitive
    conf_dict: dict = json.loads(config)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[ERROR][2025-08-19 07:47:58][app.py:66] - App startup failed!
[ERROR][2025-08-19 07:47:58][app.py:67] - Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
Traceback (most recent call last):
  File "E:\Desktop\45\dify-on-wechat\app.py", line 46, in run
    load_config()
  File "E:\Desktop\45\dify-on-wechat\config.py", line 318, in load_config
    config = Config(json.loads(config_str))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\json\__init__.py", line 335, in loads
    raise JSONDecodeError("Unexpected UTF-8 BOM (decode using utf-8-sig)",
json.decoder.JSONDecodeError: Unexpected UTF-8 BOM (decode using utf-8-sig): line 1 column 1 (char 0)
[DEBUG][2025-08-19 07:48:51][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 07:48:51][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 07:48:51][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 07:48:51][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 07:48:51][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 07:48:51][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 07:48:51][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 07:48:51][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 07:48:51][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 07:48:52][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 07:48:52][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 07:48:52][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 07:48:52][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 07:48:52][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === &wj?1TU@8Yl6 ===。
[INFO][2025-08-19 07:48:52][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 07:48:52][keyword.py:35] - [keyword]加载配置文件E:\Desktop\45\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 07:48:52][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 07:48:52][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 07:48:52][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 07:48:52][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 07:48:52][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 07:48:52][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 07:48:52][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 07:48:52][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 07:48:52][hello.py:38] - [Hello] inited
[INFO][2025-08-19 07:48:52][finish.py:23] - [Finish] inited
[INFO][2025-08-19 07:48:53][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 07:48:54][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 07:48:54][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-19 07:49:55][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 07:50:39][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'sj6albLpTkSEE5Ulvv8UtAAA***************', 'at_list': [{'nickname': '黎桂圆', 'user_id': '****************'}], 'content': 'PC计划数据导出成功\n@黎桂圆  ', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38477', 'receiver': '****************', 'send_time': '**********', 'sender': '*****************', 'sender_name': '消息通知', 'server_id': '1774686'}, 'type': 11041}
[DEBUG][2025-08-19 07:50:39][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-19 07:50:39][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18992, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 07:50:39][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-19 07:50:40][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-19 07:50:40][wework_message.py:203] - at_list: ['黎桂圆']
[DEBUG][2025-08-19 07:50:40][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-19 07:50:40][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-19 07:50:41][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=PC计划数据导出成功
@黎桂圆  , from_user_id=R:***************, from_user_nickname=SAP数据导出通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SAP数据导出通知, is_group=True, is_at=False, actual_user_id=*****************, actual_user_nickname=消息通知, at_list=['黎桂圆']
[DEBUG][2025-08-19 07:50:42][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[DEBUG][2025-08-19 07:50:42][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-19 08:58:38][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 08:58:38][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 08:58:38][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 08:58:38][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 08:58:38][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 08:58:38][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 08:58:38][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 08:58:38][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 08:58:38][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 08:58:38][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 08:58:38][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 08:58:38][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 08:58:38][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 08:58:38][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === T0JbP&!brVNG ===。
[INFO][2025-08-19 08:58:38][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 08:58:38][keyword.py:35] - [keyword]加载配置文件E:\Desktop\45\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 08:58:38][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 08:58:38][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 08:58:38][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 08:58:38][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 08:58:38][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 08:58:38][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 08:58:38][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 08:58:38][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 08:58:38][hello.py:38] - [Hello] inited
[INFO][2025-08-19 08:58:38][finish.py:23] - [Finish] inited
[INFO][2025-08-19 08:58:39][wework_channel.py:190] - 等待登录······
[DEBUG][2025-08-19 08:58:58][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 08:58:58][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 08:58:58][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 08:58:58][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 08:58:58][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 08:58:58][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 08:58:58][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 08:58:58][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 08:58:58][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 08:58:58][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 08:58:58][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 08:58:58][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 08:58:58][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 08:58:58][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === H?7!mQfTUGAe ===。
[INFO][2025-08-19 08:58:58][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 08:58:58][keyword.py:35] - [keyword]加载配置文件E:\Desktop\45\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 08:58:58][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 08:58:58][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 08:58:58][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 08:58:58][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 08:58:58][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 08:58:58][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 08:58:58][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 08:58:58][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 08:58:58][hello.py:38] - [Hello] inited
[INFO][2025-08-19 08:58:58][finish.py:23] - [Finish] inited
[INFO][2025-08-19 08:59:00][wework_channel.py:190] - 等待登录······
[DEBUG][2025-08-19 08:59:55][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 08:59:55][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 08:59:55][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 08:59:55][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 08:59:55][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 08:59:55][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 08:59:55][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 08:59:55][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 08:59:55][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 08:59:55][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 08:59:55][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 08:59:55][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 08:59:55][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 08:59:55][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === yO0Y1!e0QzK! ===。
[INFO][2025-08-19 08:59:55][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 08:59:55][keyword.py:35] - [keyword]加载配置文件E:\Desktop\45\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 08:59:55][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 08:59:55][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 08:59:55][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 08:59:55][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 08:59:55][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 08:59:56][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 08:59:56][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 08:59:56][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 08:59:56][hello.py:38] - [Hello] inited
[INFO][2025-08-19 08:59:56][finish.py:23] - [Finish] inited
[INFO][2025-08-19 08:59:56][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 08:59:57][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 08:59:57][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-19 09:00:57][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-19 09:01:04][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAMQz5mPxQYY76TDoYWAgAMg54fi/AQ=', 'at_list': [], 'content': '我手机切换企业为啥电脑的也跟着变了[苦涩]', 'content_type': 0, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38522', 'receiver': '****************', 'send_time': '1755565263', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1775076'}, 'type': 11041}
[DEBUG][2025-08-19 09:01:04][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 09:01:26][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ5ZmPxQYY/Z2EhZKAgAMgqQM=', 'at_list': [], 'content': '本来就是这样的', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38523', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '陈泽斌', 'server_id': '1775082'}, 'type': 11041}
[DEBUG][2025-08-19 09:01:26][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 09:01:26][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18836, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 09:01:26][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 09:01:26][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=本来就是这样的, from_user_id=S:****************_****************, from_user_nickname=陈泽斌, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=陈泽斌, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:01:28][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 09:01:28][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ5ZmPxQYY/Z2EhZKAgAMgqQM=", "at_list": [], "content": "本来就是这样的", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38523", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "陈泽斌", "server_id": "1775082"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=本来就是这样的, from_user_id=S:****************_****************, from_user_nickname=陈泽斌, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=陈泽斌, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:01:28][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 09:01:28][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=本来就是这样的, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4C8AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 09:01:28][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=本来就是这样的, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4C8AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 09:01:28][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:28][godcmd.py:259] - [Godcmd] on_handle_context. content: 本来就是这样的
[DEBUG][2025-08-19 09:01:28][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:28][keyword.py:53] - [keyword] on_handle_context. content: 本来就是这样的
[DEBUG][2025-08-19 09:01:28][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:28][hello.py:97] - [Hello] on_handle_context. content: 本来就是这样的
[DEBUG][2025-08-19 09:01:28][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:29][finish.py:30] - [Finish] on_handle_context. content: 本来就是这样的
[DEBUG][2025-08-19 09:01:29][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=本来就是这样的
[INFO][2025-08-19 09:01:29][bridge.py:80] - create bot mock for chat
[INFO][2025-08-19 09:01:29][mock_bot.py:21] - [MockBot] 收到消息: 本来就是这样的
[INFO][2025-08-19 09:01:29][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 09:01:29][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 09:01:29][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=本来就是这样的, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4C8AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:01:29][wework_channel.py:276] - context: Context(type=TEXT, content=本来就是这样的, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4C8AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:01:29][wework_channel.py:281] - match: None
[INFO][2025-08-19 09:01:29][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-19 09:01:29][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 09:01:29][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ6ZmPxQYY76TDoYWAgAMgAw==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38524', 'receiver': '****************', 'send_time': '1755565289', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1775085'}, 'type': 11041}
[DEBUG][2025-08-19 09:01:29][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 09:01:35][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAMQ7pmPxQYY76TDoYWAgAMgst+JvQs=', 'at_list': [], 'content': '现在好了', 'content_type': 0, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38525', 'receiver': '****************', 'send_time': '1755565295', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1775091'}, 'type': 11041}
[DEBUG][2025-08-19 09:01:35][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 09:01:40][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'wework_****************_**********_451134_11783912858409296891', 'at_list': [], 'content': '08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 0, 'local_id': '38526', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '', 'server_id': '1775093'}, 'type': 11041}
[DEBUG][2025-08-19 09:01:40][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 09:01:40][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18836, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 09:01:40][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: O:****************
[DEBUG][2025-08-19 09:01:40][wework_channel.py:95] - cmsg:ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:01:42][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 09:01:42][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "wework_****************_**********_451134_11783912858409296891", "at_list": [], "content": "08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56", "content_type": 2, "conversation_id": "O:****************", "is_pc": 0, "local_id": "38526", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "", "server_id": "1775093"}, "type": 11041}, cmsg=ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:01:42][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 09:01:42][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CB100>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 09:01:42][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CB100>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 09:01:42][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:42][godcmd.py:259] - [Godcmd] on_handle_context. content: 08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56
[DEBUG][2025-08-19 09:01:42][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:42][keyword.py:53] - [keyword] on_handle_context. content: 08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56
[DEBUG][2025-08-19 09:01:42][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:43][hello.py:97] - [Hello] on_handle_context. content: 08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56
[DEBUG][2025-08-19 09:01:43][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:01:43][finish.py:30] - [Finish] on_handle_context. content: 08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56
[DEBUG][2025-08-19 09:01:43][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56
[INFO][2025-08-19 09:01:43][mock_bot.py:21] - [MockBot] 收到消息: 08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56
[INFO][2025-08-19 09:01:43][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 09:01:43][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 09:01:43][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CB100>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:01:43][wework_channel.py:276] - context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5335:  GPO-HHC6B:  95.56, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CB100>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:01:43][wework_channel.py:281] - match: None
[INFO][2025-08-19 09:01:43][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=O:****************
[DEBUG][2025-08-19 09:01:43][chat_channel.py:323] - Worker return success, session_id = O:****************
[DEBUG][2025-08-19 09:01:43][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGAGBD3mY/FBhjvpMOhhYCAAyAE', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 1, 'local_id': '38527', 'receiver': 'O:****************', 'send_time': '1755565303', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1775095'}, 'type': 11041}
[DEBUG][2025-08-19 09:01:43][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 09:02:00][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'wework_****************_**********_936739_17035516946277476120', 'at_list': [], 'content': '08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 0, 'local_id': '38528', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '', 'server_id': '1775097'}, 'type': 11041}
[DEBUG][2025-08-19 09:02:00][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 09:02:00][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18836, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 09:02:00][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: O:****************
[DEBUG][2025-08-19 09:02:00][wework_channel.py:95] - cmsg:ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:02:01][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 09:02:01][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "wework_****************_**********_936739_17035516946277476120", "at_list": [], "content": "08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45", "content_type": 2, "conversation_id": "O:****************", "is_pc": 0, "local_id": "38528", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "", "server_id": "1775097"}, "type": 11041}, cmsg=ChatMessage: id=O:****************, create_time=**********, ctype=TEXT, content=08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45, from_user_id=O:****************, from_user_nickname=, to_user_id=****************, to_user_nickname=童树运, other_user_id=O:****************, other_user_nickname=, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:02:01][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 09:02:02][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA350>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 09:02:02][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA350>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************'})
[DEBUG][2025-08-19 09:02:02][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:02][godcmd.py:259] - [Godcmd] on_handle_context. content: 08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45
[DEBUG][2025-08-19 09:02:02][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:02][keyword.py:53] - [keyword] on_handle_context. content: 08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45
[DEBUG][2025-08-19 09:02:02][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:02][hello.py:97] - [Hello] on_handle_context. content: 08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45
[DEBUG][2025-08-19 09:02:02][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:02][finish.py:30] - [Finish] on_handle_context. content: 08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45
[DEBUG][2025-08-19 09:02:02][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45
[INFO][2025-08-19 09:02:02][mock_bot.py:21] - [MockBot] 收到消息: 08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45
[INFO][2025-08-19 09:02:02][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 09:02:02][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 09:02:02][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA350>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:02:02][wework_channel.py:276] - context: Context(type=TEXT, content=08:00----09:00  CELL <br>A5336:  MCO-HZ35:  99.45, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA350>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'O:****************', 'receiver': 'O:****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:02:02][wework_channel.py:281] - match: None
[INFO][2025-08-19 09:02:02][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=O:****************
[DEBUG][2025-08-19 09:02:02][chat_channel.py:323] - Worker return success, session_id = O:****************
[DEBUG][2025-08-19 09:02:02][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGAGBCKmo/FBhjvpMOhhYCAAyAG', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'O:****************', 'is_pc': 1, 'local_id': '38529', 'receiver': 'O:****************', 'send_time': '1755565322', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1775105'}, 'type': 11041}
[DEBUG][2025-08-19 09:02:02][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 09:02:33][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAMQqJqPxQYY76TDoYWAgAMgs5PpuwQ=', 'at_list': [], 'content': '机器人动了[憨笑]', 'content_type': 0, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38530', 'receiver': '****************', 'send_time': '1755565353', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1775109'}, 'type': 11041}
[DEBUG][2025-08-19 09:02:33][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 09:02:53][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQvZqPxQYY/Z2EhZKAgAMgrQM=', 'at_list': [], 'content': '机器人', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38532', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '陈泽斌', 'server_id': '1775118'}, 'type': 11041}
[DEBUG][2025-08-19 09:02:53][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-19 09:02:53][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 18836, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-19 09:02:53][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-19 09:02:53][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=机器人, from_user_id=S:****************_****************, from_user_nickname=陈泽斌, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=陈泽斌, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:02:56][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-19 09:02:56][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQvZqPxQYY/Z2EhZKAgAMgrQM=", "at_list": [], "content": "机器人", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38532", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "陈泽斌", "server_id": "1775118"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=机器人, from_user_id=S:****************_****************, from_user_nickname=陈泽斌, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=陈泽斌, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-19 09:02:56][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-19 09:02:56][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=机器人, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA500>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 09:02:56][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=机器人, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA500>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-19 09:02:56][plugin_manager.py:195] - Plugin GODCMD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:56][godcmd.py:259] - [Godcmd] on_handle_context. content: 机器人
[DEBUG][2025-08-19 09:02:56][plugin_manager.py:195] - Plugin KEYWORD triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:56][keyword.py:53] - [keyword] on_handle_context. content: 机器人
[DEBUG][2025-08-19 09:02:56][plugin_manager.py:195] - Plugin HELLO triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:56][hello.py:97] - [Hello] on_handle_context. content: 机器人
[DEBUG][2025-08-19 09:02:56][plugin_manager.py:195] - Plugin FINISH triggered by event Event.ON_HANDLE_CONTEXT
[DEBUG][2025-08-19 09:02:56][finish.py:30] - [Finish] on_handle_context. content: 机器人
[DEBUG][2025-08-19 09:02:56][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=机器人
[INFO][2025-08-19 09:02:56][mock_bot.py:21] - [MockBot] 收到消息: 机器人
[INFO][2025-08-19 09:02:56][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-19 09:02:56][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-19 09:02:56][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=机器人, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA500>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:02:56][wework_channel.py:276] - context: Context(type=TEXT, content=机器人, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002BC0F4CA500>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002BC0DDD97B0>})
[DEBUG][2025-08-19 09:02:56][wework_channel.py:281] - match: None
[INFO][2025-08-19 09:02:56][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-19 09:02:56][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-19 09:02:57][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQwJqPxQYY76TDoYWAgAMgBw==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38533', 'receiver': '****************', 'send_time': '1755565376', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1775121'}, 'type': 11041}
[DEBUG][2025-08-19 09:02:57][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-19 09:54:41][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 09:54:41][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 09:54:41][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 09:54:41][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 09:54:41][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 09:54:41][plugin_manager.py:50] - Loading plugins config...
[INFO][2025-08-19 09:54:41][plugin_manager.py:88] - Scaning plugins ...
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin Banwords_v1.0 registered, path=./plugins\banwords
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin BDunit_v0.1 registered, path=./plugins\bdunit
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin CustomDifyApp_v0.2 registered, path=./plugins\custom_dify_app
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin Dungeon_v1.0 registered, path=./plugins\dungeon
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin Finish_v1.0 registered, path=./plugins\finish
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin Godcmd_v1.0 registered, path=./plugins\godcmd
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin Hello_v0.1 registered, path=./plugins\hello
[WARNING][2025-08-19 09:54:41][plugin_manager.py:113] - Failed to import plugin jina_sum: No module named 'nest_asyncio'
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin Keyword_v0.1 registered, path=./plugins\keyword
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin linkai_v0.1.0 registered, path=./plugins\linkai
[INFO][2025-08-19 09:54:41][plugin_manager.py:41] - Plugin Role_v1.0 registered, path=./plugins\role
[WARNING][2025-08-19 09:54:41][plugin_manager.py:113] - Failed to import plugin tool: No module named 'chatgpt_tool_hub'
[DEBUG][2025-08-19 09:54:41][plugin_manager.py:185] - plugins.json config={'plugins': SortedDict({'Godcmd': {'enabled': True, 'priority': 999}, 'Keyword': {'enabled': True, 'priority': 900}, 'Banwords': {'enabled': False, 'priority': 100}, 'linkai': {'enabled': False, 'priority': 99}, 'Role': {'enabled': False, 'priority': 0}, 'Dungeon': {'enabled': False, 'priority': 0}, 'CustomDifyApp': {'enabled': True, 'priority': 0}, 'BDunit': {'enabled': False, 'priority': 0}, 'Hello': {'enabled': True, 'priority': -1}, 'Finish': {'enabled': True, 'priority': -999}}, sort_func=<lambda>, reverse=True)}
[DEBUG][2025-08-19 09:54:41][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\godcmd\config.json, exist=True
[DEBUG][2025-08-19 09:54:41][plugin.py:28] - loading plugin config, plugin_name=Godcmd, conf={'password': '', 'admin_users': []}
[INFO][2025-08-19 09:54:41][godcmd.py:233] - [Godcmd] 因未设置口令，本次的临时口令为 === O$MHSbben8WD ===。
[INFO][2025-08-19 09:54:41][godcmd.py:249] - [Godcmd] inited
[DEBUG][2025-08-19 09:54:41][keyword.py:35] - [keyword]加载配置文件E:\Desktop\45\dify-on-wechat\plugins\keyword\config.json
[INFO][2025-08-19 09:54:41][keyword.py:41] - [keyword] {}
[INFO][2025-08-19 09:54:41][keyword.py:43] - [keyword] inited.
[DEBUG][2025-08-19 09:54:41][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\custom_dify_app\config.json, exist=False
[DEBUG][2025-08-19 09:54:41][plugin.py:28] - loading plugin config, plugin_name=CustomDifyApp, conf=None
[INFO][2025-08-19 09:54:41][custom_dify_app.py:25] - [CustomDifyApp] config is None
[DEBUG][2025-08-19 09:54:41][plugin.py:21] - loading plugin config, plugin_config_path=./plugins\hello\config.json, exist=False
[DEBUG][2025-08-19 09:54:41][plugin.py:28] - loading plugin config, plugin_name=Hello, conf=None
[DEBUG][2025-08-19 09:54:41][hello.py:126] - No Hello plugin config.json, use plugins/hello/config.json.template
[INFO][2025-08-19 09:54:41][hello.py:38] - [Hello] inited
[INFO][2025-08-19 09:54:41][finish.py:23] - [Finish] inited
[INFO][2025-08-19 09:54:43][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 09:54:44][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 09:54:44][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-19 23:59:19][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-19 23:59:19][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-19 23:59:19][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-19 23:59:20][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-19 23:59:20][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-19 23:59:20][wework_channel.py:190] - 等待登录······
[INFO][2025-08-19 23:59:21][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-19 23:59:21][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-20 00:00:21][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 00:00:28][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQm7+SxQYY/7qbqpmAgAMguAo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38718', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776138'}, 'type': 11041}
[DEBUG][2025-08-20 00:00:28][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:00:28][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:00:28][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:00:28][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:00:30][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:00:30][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQm7+SxQYY/7qbqpmAgAMguAo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38718", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776138"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:00:30][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:00:30][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAB551D7B0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:00:30][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAB551D7B0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:00:30][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:00:30][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 00:00:30][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:00:30][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-20 00:00:30][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-20 00:00:30][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAB551D7B0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001DAAB3EF730>})
[DEBUG][2025-08-20 00:00:30][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAB551D7B0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001DAAB3EF730>})
[DEBUG][2025-08-20 00:00:30][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:00:30][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-20 00:00:30][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:00:30][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQnr+SxQYY76TDoYWAgAMgBw==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38719', 'receiver': '****************', 'send_time': '1755619230', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776141'}, 'type': 11041}
[DEBUG][2025-08-20 00:00:30][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:00:46][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQrr+SxQYY/7qbqpmAgAMgugo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38720', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776145'}, 'type': 11041}
[DEBUG][2025-08-20 00:00:46][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:00:46][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:00:46][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:00:46][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:00:47][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:00:47][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQrr+SxQYY/7qbqpmAgAMgugo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38720", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776145"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:00:47][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:00:47][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAAA024850>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:00:47][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAAA024850>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:00:47][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:00:47][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:00:47][mock_bot.py:25] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-20 00:00:47][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-20 00:00:47][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAAA024850>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001DAAB3EF730>})
[DEBUG][2025-08-20 00:00:47][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001DAAA024850>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001DAAB3EF730>})
[DEBUG][2025-08-20 00:00:47][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:00:47][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-20 00:00:47][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:00:48][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQr7+SxQYY76TDoYWAgAMgCA==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38721', 'receiver': '****************', 'send_time': '1755619248', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776148'}, 'type': 11041}
[DEBUG][2025-08-20 00:00:48][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:02:44][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:02:44][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:02:44][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:02:44][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:02:44][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:02:45][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:02:47][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:02:47][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-20 00:04:46][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:04:46][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:04:46][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:04:46][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:04:46][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:04:46][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:04:48][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:04:48][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-20 00:07:53][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:07:53][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:07:53][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:07:53][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:07:53][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:07:53][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:07:55][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:07:55][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-20 00:08:55][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 00:09:01][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQnMOSxQYY/7qbqpmAgAMgvAo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38722', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776157'}, 'type': 11041}
[DEBUG][2025-08-20 00:09:01][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:09:01][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:09:01][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:09:01][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:09:03][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:09:03][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQnMOSxQYY/7qbqpmAgAMgvAo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38722", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776157"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:09:03][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:09:03][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002854AE476A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:09:03][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002854AE476A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:09:03][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:09:03][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 00:09:03][mock_bot.py:21] - [MockBot] 收到消息: 1
[ERROR][2025-08-20 00:09:03][mock_bot.py:83] - [MockBot] 检查屏蔽列表失败: key single_chat_block_list not in available_setting
[INFO][2025-08-20 00:09:03][mock_bot.py:31] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:09:03][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:09:03][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002854AE476A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002853F80F6A0>})
[DEBUG][2025-08-20 00:09:03][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002854AE476A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002853F80F6A0>})
[DEBUG][2025-08-20 00:09:03][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:09:03][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:09:03][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:09:03][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQn8OSxQYY76TDoYWAgAMgCQ==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38723', 'receiver': '****************', 'send_time': '1755619743', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776160'}, 'type': 11041}
[DEBUG][2025-08-20 00:09:03][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:09:10][config.py:315] - [INIT] config str: {
    "channel_type": "wework",
    "debug": true,
    "model": "mock",
    "single_chat_prefix": [
        ""
    ],
    "single_chat_reply_prefix": "",
    "group_chat_prefix": [
        "@bot"
    ],
    "group_name_white_list": [
        "ALL_GROUP"
    ],
    "image_recognition": false,
    "speech_recognition": false,
    "voice_reply_voice": false,
    "auto_reply_text": "\u6211\u5df2\u7ecf\u4e0b\u73ed\uff0c\u6709\u95ee\u9898\u660e\u5929\u518d\u8bf4\uff0c\u6025\u4e8b\u8bf7\u7535\u8054"
}
[DEBUG][2025-08-20 00:09:10][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:09:10][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:09:10][config.py:264] - [Config] User datas loaded.
[INFO][2025-08-20 00:09:10][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:09:10][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:09:10][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[WARNING][2025-08-20 00:10:20][wework_channel.py:173] - 获取数据失败，重试第1次······
[WARNING][2025-08-20 00:10:35][wework_channel.py:173] - 获取数据失败，重试第2次······
[WARNING][2025-08-20 00:10:50][wework_channel.py:173] - 获取数据失败，重试第3次······
[WARNING][2025-08-20 00:11:05][wework_channel.py:173] - 获取数据失败，重试第4次······
[WARNING][2025-08-20 00:11:20][wework_channel.py:173] - 获取数据失败，重试第5次······
[DEBUG][2025-08-20 00:13:48][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:13:48][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:13:48][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:13:48][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:13:48][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:13:48][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:13:50][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:13:50][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-20 00:14:50][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 00:14:54][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ/sWSxQYY/7qbqpmAgAMgvwo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38726', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776174'}, 'type': 11041}
[DEBUG][2025-08-20 00:14:54][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:14:54][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:14:54][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:14:54][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:14:55][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:14:55][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ/sWSxQYY/7qbqpmAgAMgvwo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38726", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776174"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:14:55][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:14:55][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002227D38B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:14:55][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002227D38B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:14:55][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:14:55][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 00:14:55][mock_bot.py:21] - [MockBot] 收到消息: 1
[ERROR][2025-08-20 00:14:55][mock_bot.py:103] - [MockBot] 检查屏蔽列表失败: key single_chat_block_list not in available_setting
[INFO][2025-08-20 00:14:55][mock_bot.py:31] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:14:55][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:14:55][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002227D38B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002227C05FF70>})
[DEBUG][2025-08-20 00:14:55][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002227D38B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002227C05FF70>})
[DEBUG][2025-08-20 00:14:55][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:14:55][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:14:55][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:14:56][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ/8WSxQYY76TDoYWAgAMgCw==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38727', 'receiver': '****************', 'send_time': '1755620096', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776177'}, 'type': 11041}
[DEBUG][2025-08-20 00:14:56][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:15:06][config.py:315] - [INIT] config str: {
    "channel_type": "wework",
    "debug": true,
    "model": "mock",
    "single_chat_prefix": [
        ""
    ],
    "single_chat_reply_prefix": "",
    "group_chat_prefix": [
        "@bot"
    ],
    "group_name_white_list": [
        "ALL_GROUP"
    ],
    "image_recognition": false,
    "speech_recognition": false,
    "voice_reply_voice": false,
    "auto_reply_text": "\u6211\u5df2\u7ecf\u4e0b\u73ed\uff0c\u6709\u95ee\u9898\u660e\u5929\u518d\u8bf4\uff0c\u6025\u4e8b\u8bf7\u7535\u8054"
}
[DEBUG][2025-08-20 00:15:06][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:15:06][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:15:06][config.py:264] - [Config] User datas loaded.
[INFO][2025-08-20 00:15:06][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:15:06][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:15:06][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[WARNING][2025-08-20 00:16:16][wework_channel.py:173] - 获取数据失败，重试第1次······
[WARNING][2025-08-20 00:16:31][wework_channel.py:173] - 获取数据失败，重试第2次······
[WARNING][2025-08-20 00:16:46][wework_channel.py:173] - 获取数据失败，重试第3次······
[WARNING][2025-08-20 00:17:01][wework_channel.py:173] - 获取数据失败，重试第4次······
[WARNING][2025-08-20 00:17:16][wework_channel.py:173] - 获取数据失败，重试第5次······
[DEBUG][2025-08-20 00:18:59][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:18:59][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:18:59][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:18:59][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:18:59][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:18:59][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:19:00][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:19:00][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-20 00:19:42][config.py:338] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:19:42][config.py:340] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:19:42][config.py:264] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:19:42][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:19:42][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:19:43][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:19:44][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:19:44][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-20 00:20:45][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 00:20:49][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ4ciSxQYY/7qbqpmAgAMgwgo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38730', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776194'}, 'type': 11041}
[DEBUG][2025-08-20 00:20:49][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:20:49][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:20:49][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:20:49][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:20:51][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:20:51][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ4ciSxQYY/7qbqpmAgAMgwgo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38730", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776194"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:20:51][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:20:52][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000022299E3B700>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:20:52][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000022299E3B700>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:20:52][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:20:52][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 00:20:52][mock_bot.py:21] - [MockBot] 收到消息: 1
[WARNING][2025-08-20 00:20:52][mock_bot.py:66] - [MockBot] 获取屏蔽配置失败: key single_chat_block_list not in available_setting，使用空列表
[INFO][2025-08-20 00:20:52][mock_bot.py:41] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:20:52][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:20:52][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000022299E3B700>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002228FC4F6A0>})
[DEBUG][2025-08-20 00:20:52][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000022299E3B700>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002228FC4F6A0>})
[DEBUG][2025-08-20 00:20:52][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:20:52][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:20:52][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:20:52][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ5MiSxQYY76TDoYWAgAMgDg==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38731', 'receiver': '****************', 'send_time': '1755620452', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776197'}, 'type': 11041}
[DEBUG][2025-08-20 00:20:52][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:21:22][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:21:22][config.py:342] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:21:22][config.py:266] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:21:22][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:21:22][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:21:23][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:21:25][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:21:25][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-20 00:22:25][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 00:22:28][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQxMmSxQYY/7qbqpmAgAMgxAo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38732', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776205'}, 'type': 11041}
[DEBUG][2025-08-20 00:22:28][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:22:28][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:22:28][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:22:28][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:22:30][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:22:30][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQxMmSxQYY/7qbqpmAgAMgxAo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38732", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776205"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:22:30][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:22:30][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFDA70B7C0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:22:30][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFDA70B7C0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:22:30][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:22:30][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 00:22:30][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:22:30][mock_bot.py:41] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:22:30][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:22:30][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFDA70B7C0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001CFD0512F20>})
[DEBUG][2025-08-20 00:22:30][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFDA70B7C0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001CFD0512F20>})
[DEBUG][2025-08-20 00:22:30][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:22:30][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:22:30][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:22:30][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQxsmSxQYY76TDoYWAgAMgEA==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38733', 'receiver': '****************', 'send_time': '1755620550', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776208'}, 'type': 11041}
[DEBUG][2025-08-20 00:22:30][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:23:24][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ/MmSxQYY/7qbqpmAgAMgxgo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38734', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776214'}, 'type': 11041}
[DEBUG][2025-08-20 00:23:24][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:23:24][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:23:24][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:23:24][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:23:26][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:23:26][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ/MmSxQYY/7qbqpmAgAMgxgo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38734", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776214"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:23:26][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:23:26][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFD78C0F70>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:23:26][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFD78C0F70>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:23:26][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:23:26][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:23:26][mock_bot.py:41] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:23:26][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:23:26][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFD78C0F70>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001CFD0512F20>})
[DEBUG][2025-08-20 00:23:26][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001CFD78C0F70>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001CFD0512F20>})
[DEBUG][2025-08-20 00:23:26][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:23:26][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:23:26][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:23:27][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ/smSxQYY76TDoYWAgAMgEQ==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38735', 'receiver': '****************', 'send_time': '1755620607', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776217'}, 'type': 11041}
[DEBUG][2025-08-20 00:23:27][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:26:12][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:26:12][config.py:342] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:26:12][config.py:266] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:26:12][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:26:12][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:26:12][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:26:13][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:26:13][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-20 00:27:14][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 00:27:17][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ5cuSxQYY/7qbqpmAgAMgyAo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38736', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776223'}, 'type': 11041}
[DEBUG][2025-08-20 00:27:17][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:27:17][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:27:17][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:27:17][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:27:19][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:27:19][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ5cuSxQYY/7qbqpmAgAMgyAo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38736", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776223"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:27:19][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:27:19][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000288C531B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:27:19][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000288C531B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:27:19][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:27:19][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 00:27:19][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:27:19][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 00:27:19][mock_bot.py:76] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 00:27:19][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 00:27:19][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:27:19][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:27:19][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000288C531B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000288BB19F6A0>})
[DEBUG][2025-08-20 00:27:19][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000288C531B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000288BB19F6A0>})
[DEBUG][2025-08-20 00:27:19][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:27:19][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:27:19][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:27:19][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ58uSxQYY76TDoYWAgAMgEg==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38737', 'receiver': '****************', 'send_time': '1755620839', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776226'}, 'type': 11041}
[DEBUG][2025-08-20 00:27:19][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:27:37][config.py:317] - [INIT] config str: {
    "channel_type": "wework",
    "debug": true,
    "model": "mock",
    "single_chat_prefix": [
        ""
    ],
    "single_chat_reply_prefix": "",
    "group_chat_prefix": [
        "@bot"
    ],
    "group_name_white_list": [
        "ALL_GROUP"
    ],
    "image_recognition": false,
    "speech_recognition": false,
    "voice_reply_voice": false,
    "auto_reply_text": "\u6211\u5df2\u7ecf\u4e0b\u73ed\uff0c\u6709\u95ee\u9898\u660e\u5929\u518d\u8bf4\uff0c\u6025\u4e8b\u8bf7\u7535\u8054"
}
[DEBUG][2025-08-20 00:27:37][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:27:37][config.py:342] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:27:37][config.py:266] - [Config] User datas loaded.
[INFO][2025-08-20 00:27:37][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:27:37][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:27:37][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-20 00:29:02][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 00:29:02][config.py:342] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 00:29:02][config.py:266] - [Config] User datas loaded.
[DEBUG][2025-08-20 00:29:02][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 00:29:02][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 00:29:02][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 00:29:04][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 00:29:04][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[INFO][2025-08-20 00:30:04][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 00:30:08][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQkM2SxQYY/7qbqpmAgAMgywo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38739', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776238'}, 'type': 11041}
[DEBUG][2025-08-20 00:30:08][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:30:08][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:30:08][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:30:08][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:30:09][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:30:09][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQkM2SxQYY/7qbqpmAgAMgywo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38739", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776238"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:30:09][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:30:09][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:30:09][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:30:09][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:30:09][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 00:30:09][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:30:09][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 00:30:09][mock_bot.py:76] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 00:30:09][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 00:30:09][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:30:09][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:30:09][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:30:09][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0B6A0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:30:09][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:30:09][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:30:09][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:30:10][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQkc2SxQYY76TDoYWAgAMgFA==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38740', 'receiver': '****************', 'send_time': '1755621009', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776241'}, 'type': 11041}
[DEBUG][2025-08-20 00:30:10][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:30:19][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQms2SxQYY/7qbqpmAgAMgzQo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38741', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776245'}, 'type': 11041}
[DEBUG][2025-08-20 00:30:19][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:30:19][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:30:19][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:30:19][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:30:20][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:30:20][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQms2SxQYY/7qbqpmAgAMgzQo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38741", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776245"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:30:20][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:30:20][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41E8F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:30:20][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41E8F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:30:20][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:30:20][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:30:20][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 00:30:20][mock_bot.py:79] - [MockBot] 屏蔽检查 - 单聊屏蔽列表: ['尚二松(18378950064)']
[INFO][2025-08-20 00:30:20][mock_bot.py:80] - [MockBot] 屏蔽检查 - 群聊屏蔽列表: []
[INFO][2025-08-20 00:30:20][mock_bot.py:102] - [MockBot] 消息类型检查 - is_group: False
[INFO][2025-08-20 00:30:20][mock_bot.py:131] - [MockBot] 单聊消息，用户: '尚二松'
[INFO][2025-08-20 00:30:20][mock_bot.py:137] - [MockBot] 单聊用户 '尚二松' 不在屏蔽列表中
[DEBUG][2025-08-20 00:30:20][mock_bot.py:145] - [MockBot] 消息未被屏蔽
[INFO][2025-08-20 00:30:20][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 00:30:20][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:30:20][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:30:20][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41E8F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:30:20][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41E8F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:30:20][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:30:20][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:30:20][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:30:20][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQnM2SxQYY76TDoYWAgAMgFQ==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38742', 'receiver': '****************', 'send_time': '1755621020', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776248'}, 'type': 11041}
[DEBUG][2025-08-20 00:30:20][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:30:57][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQwM2SxQYY/7qbqpmAgAMgzwo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38743', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776255'}, 'type': 11041}
[DEBUG][2025-08-20 00:30:57][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:30:57][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:30:57][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:30:57][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:30:58][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:30:58][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQwM2SxQYY/7qbqpmAgAMgzwo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38743", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776255"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:30:58][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:30:58][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41EE60>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:30:58][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41EE60>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:30:58][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:30:58][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:30:58][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 00:30:58][mock_bot.py:79] - [MockBot] 屏蔽检查 - 单聊屏蔽列表: ['尚二松(18378950064)', '尚二松']
[INFO][2025-08-20 00:30:58][mock_bot.py:80] - [MockBot] 屏蔽检查 - 群聊屏蔽列表: []
[INFO][2025-08-20 00:30:58][mock_bot.py:102] - [MockBot] 消息类型检查 - is_group: False
[INFO][2025-08-20 00:30:58][mock_bot.py:131] - [MockBot] 单聊消息，用户: '尚二松'
[INFO][2025-08-20 00:30:58][mock_bot.py:134] - [MockBot] 单聊用户 '尚二松' 在屏蔽列表中
[INFO][2025-08-20 00:30:58][mock_bot.py:27] - [MockBot] 屏蔽检查结果: True
[INFO][2025-08-20 00:30:58][mock_bot.py:29] - [MockBot] 消息已被屏蔽，不进行自动回复
[DEBUG][2025-08-20 00:30:58][chat_channel.py:177] - [chat_channel] ready to decorate reply: None
[DEBUG][2025-08-20 00:30:58][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:31:19][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ182SxQYY/7qbqpmAgAMg0Ao=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38744', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776258'}, 'type': 11041}
[DEBUG][2025-08-20 00:31:19][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 00:31:19][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:31:19][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 00:31:19][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:31:21][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 00:31:21][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ182SxQYY/7qbqpmAgAMg0Ao=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38744", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776258"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 00:31:21][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 00:31:21][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0BBB0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:31:21][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0BBB0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 00:31:21][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 00:31:21][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 00:31:21][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 00:31:21][mock_bot.py:76] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 00:31:21][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 00:31:21][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:31:21][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:31:21][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0BBB0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:31:21][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0BBB0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:31:21][wework_channel.py:281] - match: None
[INFO][2025-08-20 00:31:21][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 00:31:21][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 00:31:21][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ2c2SxQYY76TDoYWAgAMgFg==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38745', 'receiver': '****************', 'send_time': '1755621081', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776261'}, 'type': 11041}
[DEBUG][2025-08-20 00:31:21][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:31:41][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBDtzZLFBhj/upuqmYCAAyDTCg==', 'at_list': [{'nickname': '童树运', 'user_id': '****************'}], 'content': '@童树运  ', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38746', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776268'}, 'type': 11041}
[DEBUG][2025-08-20 00:31:41][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-20 00:31:41][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:31:41][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-20 00:31:42][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手(TEST)', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-20 00:31:42][wework_message.py:203] - at_list: ['童树运']
[DEBUG][2025-08-20 00:31:42][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-20 00:31:42][wework_message.py:215] - Wechaty message R:*************** includes at
[DEBUG][2025-08-20 00:31:42][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-20 00:31:42][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=@童树运  , from_user_id=R:***************, from_user_nickname=SSH推送通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SSH推送通知, is_group=True, is_at=True, actual_user_id=****************, actual_user_nickname=尚二松, at_list=['童树运']
[DEBUG][2025-08-20 00:31:44][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[INFO][2025-08-20 00:31:44][chat_channel.py:117] - [chat_channel]receive group at
[DEBUG][2025-08-20 00:31:44][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-20 00:31:44][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000019119B0BB50>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************'})
[DEBUG][2025-08-20 00:31:44][chat_channel.py:323] - Worker return success, session_id = ****************@@R:***************
[DEBUG][2025-08-20 00:32:06][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBCGzpLFBhj/upuqmYCAAyDUCg==', 'at_list': [{'nickname': '童树运', 'user_id': '****************'}], 'content': '@童树运  1313', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38747', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776275'}, 'type': 11041}
[DEBUG][2025-08-20 00:32:06][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-20 00:32:06][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:32:06][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-20 00:32:07][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手(TEST)', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-20 00:32:07][wework_message.py:203] - at_list: ['童树运']
[DEBUG][2025-08-20 00:32:07][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-20 00:32:07][wework_message.py:215] - Wechaty message R:*************** includes at
[DEBUG][2025-08-20 00:32:07][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-20 00:32:07][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=@童树运  1313, from_user_id=R:***************, from_user_nickname=SSH推送通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SSH推送通知, is_group=True, is_at=True, actual_user_id=****************, actual_user_nickname=尚二松, at_list=['童树运']
[DEBUG][2025-08-20 00:32:09][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[INFO][2025-08-20 00:32:09][chat_channel.py:117] - [chat_channel]receive group at
[DEBUG][2025-08-20 00:32:09][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-20 00:32:09][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1313, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41FBE0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************'})
[DEBUG][2025-08-20 00:32:09][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1313, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41FBE0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************'})
[DEBUG][2025-08-20 00:32:09][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1313
[INFO][2025-08-20 00:32:09][mock_bot.py:21] - [MockBot] 收到消息: 1313
[INFO][2025-08-20 00:32:09][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 00:32:09][mock_bot.py:76] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 00:32:09][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 00:32:09][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 00:32:09][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 00:32:09][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=@尚二松
我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1313, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41FBE0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:32:09][wework_channel.py:276] - context: Context(type=TEXT, content=1313, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41FBE0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001910F92F6A0>})
[DEBUG][2025-08-20 00:32:09][wework_channel.py:281] - match: <re.Match object; span=(0, 5), match='@尚二松\n'>
[DEBUG][2025-08-20 00:32:09][wework_channel.py:285] - new_content: 
我已经下班，有问题明天再说，急事请电
[INFO][2025-08-20 00:32:09][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=@尚二松
我已经下班，有问题明天再说，急事请电), receiver=R:***************
[DEBUG][2025-08-20 00:32:09][chat_channel.py:323] - Worker return success, session_id = ****************@@R:***************
[DEBUG][2025-08-20 00:32:09][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBCJzpLFBhjvpMOhhYCAAyAY', 'at_list': [{'nickname': '尚二松', 'user_id': '****************'}], 'content': '@尚二松 \n我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 1, 'local_id': '38748', 'receiver': 'R:***************', 'send_time': '1755621129', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776278'}, 'type': 11041}
[DEBUG][2025-08-20 00:32:09][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 00:32:27][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CIGABBCbzpLFBhj/upuqmYCAAyDWCg==', 'at_list': [{'nickname': '童树运', 'user_id': '****************'}], 'content': '@童树运  4949', 'content_type': 2, 'conversation_id': 'R:***************', 'is_pc': 0, 'local_id': '38749', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776284'}, 'type': 11041}
[DEBUG][2025-08-20 00:32:27][wework_channel.py:93] - 正在为群聊创建 WeworkMessage
[DEBUG][2025-08-20 00:32:27][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 00:32:27][wework_message.py:28] - 传入的 conversation_id: R:***************
[DEBUG][2025-08-20 00:32:28][wework_message.py:34] - 获取到的群聊信息: {'page_num': 1, 'page_size': 500, 'room_list': [{'conversation_id': 'R:**************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BKBGMM8 外箱标签', 'total': 15}, {'conversation_id': 'R:***************', 'create_time': **********, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ客户上传数据超时', 'total': 4}, {'conversation_id': 'R:239202935514633', 'create_time': 1725929185, 'create_user_id': '1688853356870344', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'NAP-ENG一二职级通知群', 'total': 25}, {'conversation_id': 'R:263841105481753', 'create_time': 1742437196, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运', 'total': 1}, {'conversation_id': 'R:138366661454818', 'create_time': 1700711529, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '机器人验证', 'total': 3}, {'conversation_id': 'R:14212680990631', 'create_time': 1753497518, 'create_user_id': '1688853356869379', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FV9上传尺寸字段沟通', 'total': 19}, {'conversation_id': 'R:195815832071930', 'create_time': 1752087452, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': '童树运、胡波锋、1131', 'total': 3}, {'conversation_id': 'R:158758549245160', 'create_time': 1745229100, 'create_user_id': '1688857762238648', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'BU1 MMUO 夜班稽核组', 'total': 34}, {'conversation_id': 'R:244675651818107', 'create_time': 1693532572, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'AMZ数据沟通群', 'total': 14}, {'conversation_id': 'R:232095519764999', 'create_time': 1735114996, 'create_user_id': '1688853356864639', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MBUO MES2.0推广沟通群', 'total': 109}, {'conversation_id': 'R:4439413948896', 'create_time': 1750232850, 'create_user_id': '1688857612471300', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FRL 使用NVT出货标签', 'total': 19}, {'conversation_id': 'R:252952924667949', 'create_time': 1685585043, 'create_user_id': '1688853356875460', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMU TE沟通群', 'total': 63}, {'conversation_id': 'R:122947230624658', 'create_time': 1754970305, 'create_user_id': '1688853356865190', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '八月份办上岗证人员', 'total': 15}, {'conversation_id': 'R:153774473702584', 'create_time': 1657518209, 'create_user_id': '1688850016721623', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'FineBI新版本测试沟通群', 'total': 300}, {'conversation_id': 'R:227187827803597', 'create_time': 1749274610, 'create_user_id': '1688853356870955', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUO 辅助部门考勤异常通知群', 'total': 52}, {'conversation_id': 'R:261321576643906', 'create_time': 1700179260, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '功能送修拉通群', 'total': 26}, {'conversation_id': 'R:***************', 'create_time': 1702168658, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SAP数据导出通知', 'total': 3}, {'conversation_id': 'R:205135122062725', 'create_time': 1699065050, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'Kazam数据验证', 'total': 6}, {'conversation_id': 'R:179875872853721', 'create_time': 1755326540, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': '童树运、陈泽斌、IT小助手(TEST)', 'total': 3}, {'conversation_id': 'R:45225352600155', 'create_time': 1742222957, 'create_user_id': '1688853356868641', 'is_admin': 0, 'is_creator': 0, 'is_external': 0, 'nickname': 'MMUOTE沟通群', 'total': 13}, {'conversation_id': 'R:***************', 'create_time': 1734915605, 'create_user_id': '****************', 'is_admin': 0, 'is_creator': 1, 'is_external': 0, 'nickname': 'SSH推送通知', 'total': 5}], 'total': 21, 'total_page': 1}
[DEBUG][2025-08-20 00:32:28][wework_message.py:203] - at_list: ['童树运']
[DEBUG][2025-08-20 00:32:28][wework_message.py:204] - nickname: 童树运
[DEBUG][2025-08-20 00:32:28][wework_message.py:215] - Wechaty message R:*************** includes at
[DEBUG][2025-08-20 00:32:28][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: R:***************
[DEBUG][2025-08-20 00:32:28][wework_channel.py:95] - cmsg:ChatMessage: id=R:***************, create_time=**********, ctype=TEXT, content=@童树运  4949, from_user_id=R:***************, from_user_nickname=SSH推送通知, to_user_id=****************, to_user_nickname=童树运, other_user_id=R:***************, other_user_nickname=SSH推送通知, is_group=True, is_at=True, actual_user_id=****************, actual_user_nickname=尚二松, at_list=['童树运']
[DEBUG][2025-08-20 00:32:30][wework_channel.py:100] - 准备用 WeworkChannel 处理群聊消息
[INFO][2025-08-20 00:32:30][chat_channel.py:117] - [chat_channel]receive group at
[DEBUG][2025-08-20 00:32:30][wework_channel.py:105] - 已用 WeworkChannel 处理完群聊消息
[DEBUG][2025-08-20 00:32:31][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=4949, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41EDD0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************'})
[DEBUG][2025-08-20 00:32:31][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=4949, kwargs={'isgroup': True, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001910E41EDD0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'group_name': 'SSH推送通知', 'is_shared_session_group': False, 'session_id': '****************@@R:***************', 'receiver': 'R:***************'})
[DEBUG][2025-08-20 00:32:31][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=4949
[INFO][2025-08-20 00:32:31][mock_bot.py:21] - [MockBot] 收到消息: 4949
[INFO][2025-08-20 00:32:31][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 00:32:31][mock_bot.py:79] - [MockBot] 屏蔽检查 - 单聊屏蔽列表: []
[INFO][2025-08-20 00:32:31][mock_bot.py:80] - [MockBot] 屏蔽检查 - 群聊屏蔽列表: ['SSH推送通知']
[INFO][2025-08-20 00:32:31][mock_bot.py:102] - [MockBot] 消息类型检查 - is_group: True
[INFO][2025-08-20 00:32:31][mock_bot.py:115] - [MockBot] 群聊消息，群名: 'SSH推送通知'
[INFO][2025-08-20 00:32:31][mock_bot.py:118] - [MockBot] 群聊 'SSH推送通知' 在屏蔽列表中
[INFO][2025-08-20 00:32:31][mock_bot.py:27] - [MockBot] 屏蔽检查结果: True
[INFO][2025-08-20 00:32:31][mock_bot.py:29] - [MockBot] 消息已被屏蔽，不进行自动回复
[DEBUG][2025-08-20 00:32:31][chat_channel.py:177] - [chat_channel] ready to decorate reply: None
[DEBUG][2025-08-20 00:32:31][chat_channel.py:323] - Worker return success, session_id = ****************@@R:***************
[DEBUG][2025-08-20 01:24:47][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 01:24:47][config.py:342] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 01:24:47][config.py:266] - [Config] User datas loaded.
[DEBUG][2025-08-20 01:24:47][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 01:24:47][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 01:24:47][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 01:24:48][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 01:24:48][wework_channel.py:196] - 静默延迟60s，等待客户端刷新数据，请勿进行任何操作······
[DEBUG][2025-08-20 01:27:35][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 01:27:35][config.py:342] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 01:27:35][config.py:266] - [Config] User datas loaded.
[DEBUG][2025-08-20 01:27:36][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 01:27:36][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 01:27:37][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 01:27:38][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 01:27:38][wework_channel.py:196] - 企业微信登录成功，开始获取联系人和群聊信息...
[WARNING][2025-08-20 01:27:38][wework_channel.py:173] - 获取数据失败，重试第1次······
[INFO][2025-08-20 01:27:43][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 01:28:07][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQp+iSxQYY/7qbqpmAgAMg1wo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38750', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776309'}, 'type': 11041}
[DEBUG][2025-08-20 01:28:07][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 01:28:07][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 01:28:07][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 01:28:07][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 01:28:09][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 01:28:09][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQp+iSxQYY/7qbqpmAgAMg1wo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38750", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776309"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 01:28:09][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 01:28:10][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A4AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 01:28:10][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A4AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 01:28:10][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 01:28:10][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 01:28:10][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 01:28:10][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 01:28:10][mock_bot.py:76] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 01:28:10][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 01:28:10][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 01:28:10][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 01:28:10][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A4AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002B5570D1BD0>})
[DEBUG][2025-08-20 01:28:10][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A4AC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002B5570D1BD0>})
[DEBUG][2025-08-20 01:28:10][wework_channel.py:281] - match: None
[INFO][2025-08-20 01:28:10][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 01:28:10][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 01:28:10][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQquiSxQYY76TDoYWAgAMgGg==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38751', 'receiver': '****************', 'send_time': '1755624490', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776312'}, 'type': 11041}
[DEBUG][2025-08-20 01:28:10][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 01:28:21][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQteiSxQYY/7qbqpmAgAMg2Qo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38752', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776316'}, 'type': 11041}
[DEBUG][2025-08-20 01:28:21][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 01:28:21][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 01:28:21][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 01:28:21][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 01:28:22][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 01:28:22][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQteiSxQYY/7qbqpmAgAMg2Qo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38752", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776316"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 01:28:22][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 01:28:22][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A7B80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 01:28:22][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A7B80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 01:28:22][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 01:28:22][mock_bot.py:21] - [MockBot] 收到消息: 1
[INFO][2025-08-20 01:28:22][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 01:28:22][mock_bot.py:79] - [MockBot] 屏蔽检查 - 单聊屏蔽列表: []
[INFO][2025-08-20 01:28:22][mock_bot.py:80] - [MockBot] 屏蔽检查 - 群聊屏蔽列表: ['SSH推送通知']
[INFO][2025-08-20 01:28:22][mock_bot.py:102] - [MockBot] 消息类型检查 - is_group: False
[INFO][2025-08-20 01:28:22][mock_bot.py:131] - [MockBot] 单聊消息，用户: '尚二松'
[INFO][2025-08-20 01:28:22][mock_bot.py:137] - [MockBot] 单聊用户 '尚二松' 不在屏蔽列表中
[DEBUG][2025-08-20 01:28:22][mock_bot.py:145] - [MockBot] 消息未被屏蔽
[INFO][2025-08-20 01:28:22][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 01:28:22][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请
[DEBUG][2025-08-20 01:28:22][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请)
[DEBUG][2025-08-20 01:28:22][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A7B80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002B5570D1BD0>})
[DEBUG][2025-08-20 01:28:22][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000002B5633A7B80>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000002B5570D1BD0>})
[DEBUG][2025-08-20 01:28:22][wework_channel.py:281] - match: None
[INFO][2025-08-20 01:28:22][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请), receiver=S:****************_****************
[DEBUG][2025-08-20 01:28:22][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 01:28:22][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQtuiSxQYY76TDoYWAgAMgGw==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38753', 'receiver': '****************', 'send_time': '1755624502', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776319'}, 'type': 11041}
[DEBUG][2025-08-20 01:28:22][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 02:03:25][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 02:03:25][config.py:342] - [INIT] load config: {'channel_type': 'wework', 'debug': True, 'model': 'mock', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': False, 'speech_recognition': False, 'voice_reply_voice': False, 'auto_reply_text': '我已经下班，有问题明天再说，急事请电联'}
[INFO][2025-08-20 02:03:25][config.py:266] - [Config] User datas loaded.
[DEBUG][2025-08-20 02:03:25][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[WARNING][2025-08-20 02:03:25][audio_convert.py:15] - import pydub failed, wechat voice conversion will not be supported. Try: pip install pydub
[INFO][2025-08-20 02:03:26][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 02:03:27][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 02:03:27][wework_channel.py:196] - 企业微信登录成功，开始获取联系人和群聊信息...
[WARNING][2025-08-20 02:03:27][wework_channel.py:173] - 获取数据失败，重试第1次······
[INFO][2025-08-20 02:03:32][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 02:03:56][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQjPmSxQYY/7qbqpmAgAMg3Qo=', 'at_list': [], 'content': '11', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38754', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776323'}, 'type': 11041}
[DEBUG][2025-08-20 02:03:56][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 02:03:56][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 02:03:56][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 02:03:56][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=11, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 02:03:58][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 02:03:58][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQjPmSxQYY/7qbqpmAgAMg3Qo=", "at_list": [], "content": "11", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38754", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776323"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=11, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 02:03:58][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 02:03:58][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=11, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000152C3671FC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 02:03:58][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=11, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000152C3671FC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 02:03:58][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=11
[INFO][2025-08-20 02:03:58][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 02:03:58][mock_bot.py:21] - [MockBot] 收到消息: 11
[INFO][2025-08-20 02:03:58][mock_bot.py:25] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 02:03:58][mock_bot.py:76] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 02:03:58][mock_bot.py:27] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 02:03:58][mock_bot.py:45] - [MockBot] 回复消息: 我已经下班，有问题明天再说，急事请
[DEBUG][2025-08-20 02:03:58][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请)
[DEBUG][2025-08-20 02:03:58][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请), context: Context(type=TEXT, content=11, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000152C3671FC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000152B706F100>})
[DEBUG][2025-08-20 02:03:58][wework_channel.py:276] - context: Context(type=TEXT, content=11, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x00000152C3671FC0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x00000152B706F100>})
[DEBUG][2025-08-20 02:03:58][wework_channel.py:281] - match: None
[INFO][2025-08-20 02:03:58][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请), receiver=S:****************_****************
[DEBUG][2025-08-20 02:03:58][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 02:03:59][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQjvmSxQYY76TDoYWAgAMgHA==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38755', 'receiver': '****************', 'send_time': '1755626639', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776326'}, 'type': 11041}
[DEBUG][2025-08-20 02:03:59][wework_channel.py:129] - 自己发的，直接结束
