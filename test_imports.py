#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有模块导入是否正常
"""

def test_imports():
    """测试所有关键模块的导入"""
    print("开始测试模块导入...")
    
    try:
        # 测试translate模块
        from translate.factory import create_translator
        print("✅ translate.factory 导入成功")
    except ImportError as e:
        print(f"❌ translate.factory 导入失败: {e}")
    
    try:
        # 测试voice模块
        from voice.factory import create_voice
        print("✅ voice.factory 导入成功")
    except ImportError as e:
        print(f"❌ voice.factory 导入失败: {e}")
    
    try:
        # 测试plugins模块
        from plugins import PluginManager
        print("✅ plugins.PluginManager 导入成功")
    except ImportError as e:
        print(f"❌ plugins.PluginManager 导入失败: {e}")
    
    try:
        # 测试config模块
        from config import load_config, conf
        print("✅ config 模块导入成功")
    except ImportError as e:
        print(f"❌ config 模块导入失败: {e}")
    
    try:
        # 测试bridge模块
        from bridge.bridge import Bridge
        print("✅ bridge.Bridge 导入成功")
    except ImportError as e:
        print(f"❌ bridge.Bridge 导入失败: {e}")
    
    print("模块导入测试完成！")

if __name__ == "__main__":
    test_imports()
