# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller 打包配置文件
用于将企业微信自动回复系统打包成单目录可执行文件
"""

import os
import sys
from PyInstaller.building.build_main import Analysis, PYZ, EXE, COLLECT

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))

# 主程序入口
main_script = os.path.join(project_root, 'gui_app.py')

# 需要包含的数据文件
datas = [
    # 配置文件
    (os.path.join(project_root, '*.json'), '.'),
    (os.path.join(project_root, '*.ico'), '.'),
    
    # 模板和资源文件
    (os.path.join(project_root, 'config.py'), '.'),
    (os.path.join(project_root, 'README.md'), '.'),
    
    # GUI相关文件
    (os.path.join(project_root, 'gui'), 'gui'),
    (os.path.join(project_root, 'bot'), 'bot'),
    (os.path.join(project_root, 'bridge'), 'bridge'),
    (os.path.join(project_root, 'channel'), 'channel'),
    (os.path.join(project_root, 'common'), 'common'),
    (os.path.join(project_root, 'lib'), 'lib'),
    (os.path.join(project_root, 'plugins'), 'plugins'),
    (os.path.join(project_root, 'voice'), 'voice'),
]

# 需要包含的隐藏导入
hiddenimports = [
    # PyQt5相关
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    
    # 企业微信相关
    'ntwork',
    'wework',
    
    # 其他依赖
    'requests',
    'json',
    'logging',
    'threading',
    'datetime',
    'os',
    'sys',
    'time',
    'traceback',
    'importlib',
    
    # 项目模块
    'bot.mock.mock_bot',
    'bridge.bridge',
    'bridge.context',
    'bridge.reply',
    'channel.wework.wework_channel',
    'channel.wework.wework_message',
    'common.log',
    'common.singleton',
    'config',
    'gui.main_window',
    'gui.managers',
    'gui.system_tray',
]

# 需要排除的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'cv2',
]

# 分析配置
a = Analysis(
    [main_script],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 去除重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, '2.ico') if os.path.exists(os.path.join(project_root, '2.ico')) else None,
)

# 收集所有文件到单目录
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复系统',
)
