# encoding:utf-8

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, 
                             QPushButton, QLabel, QTabWidget, QWidget, QScrollArea)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap, QIcon
import os

class HelpWindow(QDialog):
    """使用说明窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("企业微信自动回复系统 - 使用说明")
        self.setGeometry(200, 200, 800, 600)
        self.setMinimumSize(750, 550)
        
        # 设置窗口图标
        self.set_window_icon()
        
        # 设置样式
        self.setStyleSheet(self.get_style())
        
        # 创建界面
        self.init_ui()
        
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "2.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置帮助窗口图标失败: {e}")
    
    def get_style(self):
        """获取样式表"""
        return """
            QDialog {
                background-color: #f5f5f5;
            }
            
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
                margin-top: -1px;
            }
            
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                color: #3498db;
                border-bottom: 2px solid #3498db;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #d5dbdb;
            }
            
            QTextEdit {
                border: none;
                background-color: white;
                padding: 15px;
                font-size: 10pt;
                line-height: 1.6;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 10pt;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                font-size: 12pt;
            }
        """
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel("📖 企业微信自动回复系统使用说明")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16pt; color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建各个标签页
        self.create_quick_start_tab()
        self.create_features_tab()
        self.create_config_tab()
        self.create_troubleshooting_tab()
        self.create_support_tab()
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
    
    def create_quick_start_tab(self):
        """创建快速开始标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml("""
        <h2>� 按钮功能说明</h2>

        <h3>🚀 运行界面按钮</h3>

        <h4>� 启动服务</h4>
        <ul>
            <li><strong>功能</strong>：启动企业微信自动回复服务</li>
            <li><strong>操作</strong>：点击后开始30秒倒计时，倒计时结束后自动连接企业微信</li>
            <li><strong>状态变化</strong>：按钮变为"倒计时中..."，无法重复点击</li>
            <li><strong>完成标志</strong>：看到"企业微信程序初始化完成"并且日志清空</li>
        </ul>

        <h4>⏹️ 停止服务</h4>
        <ul>
            <li><strong>功能</strong>：停止自动回复服务或取消倒计时</li>
            <li><strong>倒计时期间</strong>：显示为"取消倒计时"，点击可中断启动过程</li>
            <li><strong>服务运行期间</strong>：显示为"停止服务"，点击可停止自动回复</li>
            <li><strong>效果</strong>：停止后统计信息重置，不再自动回复消息</li>
        </ul>

        <h4>� 已停止/运行中</h4>
        <ul>
            <li><strong>功能</strong>：显示当前服务状态</li>
            <li><strong>已停止</strong>：服务未运行，不会自动回复</li>
            <li><strong>运行中</strong>：服务正常运行，会自动回复消息</li>
            <li><strong>注意</strong>：这是状态显示按钮，不可点击</li>
        </ul>

        <h4>❓ 使用说明</h4>
        <ul>
            <li><strong>功能</strong>：打开本帮助窗口</li>
            <li><strong>内容</strong>：包含详细的功能说明和使用方法</li>
            <li><strong>随时可用</strong>：任何时候都可以点击查看帮助</li>
        </ul>

        <h3>⚙️ 配置界面按钮</h3>

        <h4>💾 保存配置</h4>
        <ul>
            <li><strong>位置</strong>：基本配置区域</li>
            <li><strong>功能</strong>：保存自动回复的文本内容</li>
            <li><strong>生效时间</strong>：立即生效，无需重启服务</li>
            <li><strong>提示</strong>：修改回复内容后必须点击此按钮才能保存</li>
        </ul>

        <h4>添加/删除选中（屏蔽设置）</h4>
        <ul>
            <li><strong>添加按钮</strong>：将输入框中的用户名或群聊名添加到屏蔽列表</li>
            <li><strong>删除选中按钮</strong>：删除列表中选中的屏蔽项</li>
            <li><strong>注意</strong>：用户名和群聊名必须与企业微信中显示的完全一致</li>
        </ul>

        <h4>💾 保存屏蔽配置</h4>
        <ul>
            <li><strong>功能</strong>：保存所有屏蔽设置</li>
            <li><strong>包含</strong>：单聊屏蔽列表和群聊屏蔽列表</li>
            <li><strong>生效时间</strong>：立即生效，被屏蔽的用户/群聊不会收到自动回复</li>
        </ul>

        <div style="background-color: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>💡 使用提示</strong>：
            <ul>
                <li>先配置回复内容和屏蔽设置，再启动服务</li>
                <li>服务运行期间也可以随时修改配置</li>
                <li>所有配置修改后都需要点击对应的保存按钮</li>
            </ul>
        </div>
        """)
        
        layout.addWidget(text_edit)
        self.tab_widget.addTab(widget, "� 按钮功能")
    
    def create_features_tab(self):
        """创建功能介绍标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml("""
        <h2>⭐ 功能特性</h2>
        
        <h3>🤖 自动回复功能</h3>
        <ul>
            <li><strong>单聊自动回复</strong>：自动回复私聊消息</li>
            <li><strong>群聊自动回复</strong>：自动回复群聊中的消息</li>
            <li><strong>自定义回复内容</strong>：可以设置个性化的回复文本</li>
            <li><strong>实时配置更新</strong>：修改配置后立即生效，无需重启</li>
        </ul>
        
        <h3>🚫 智能屏蔽功能</h3>
        <ul>
            <li><strong>用户屏蔽</strong>：指定用户的消息不进行自动回复</li>
            <li><strong>群聊屏蔽</strong>：指定群聊的消息不进行自动回复</li>
            <li><strong>精确匹配</strong>：基于用户昵称和群聊名称进行精确匹配</li>
            <li><strong>灵活管理</strong>：可随时添加或删除屏蔽项</li>
        </ul>
        
        <h3>📊 运行统计功能</h3>
        <ul>
            <li><strong>运行时长</strong>：实时显示服务运行时间（HH:MM:SS格式）</li>
            <li><strong>接收消息数</strong>：统计接收到的消息总数</li>
            <li><strong>回复消息数</strong>：统计成功回复的消息总数</li>
            <li><strong>成功率</strong>：计算回复成功率百分比</li>
        </ul>
        
        <h3>📋 日志记录功能</h3>
        <ul>
            <li><strong>实时日志</strong>：显示系统运行状态和消息处理记录</li>
            <li><strong>详细信息</strong>：包含用户昵称、群聊名称、消息内容等</li>
            <li><strong>状态标识</strong>：使用表情符号区分不同类型的操作</li>
            <li><strong>自动清空</strong>：服务启动完成后自动清空启动日志</li>
        </ul>
        
        <h3>🎨 界面功能</h3>
        <ul>
            <li><strong>双标签页设计</strong>：运行界面和配置界面分离</li>
            <li><strong>倒计时显示</strong>：30秒启动倒计时，避免冲突</li>
            <li><strong>系统托盘</strong>：支持最小化到系统托盘运行</li>
            <li><strong>现代化界面</strong>：卡片式设计，美观易用</li>
        </ul>
        
        <div style="background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>⚠️ 注意</strong>：本系统仅适用于企业微信，不支持个人微信。
        </div>
        """)
        
        layout.addWidget(text_edit)
        self.tab_widget.addTab(widget, "⭐ 功能特性")

    def create_config_tab(self):
        """创建配置说明标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml("""
        <h2>⚙️ 配置说明</h2>

        <h3>📝 基本配置</h3>
        <h4>回复内容设置</h4>
        <ul>
            <li><strong>位置</strong>：配置界面 → 基本配置</li>
            <li><strong>功能</strong>：设置自动回复的文本内容</li>
            <li><strong>示例</strong>：
                <ul>
                    <li>"我已经下班，有问题明天再说，急事请电联"</li>
                    <li>"正在开会中，稍后回复您"</li>
                    <li>"我在休假中，紧急事务请联系我的同事"</li>
                </ul>
            </li>
            <li><strong>注意</strong>：修改后需要点击"保存配置"才能生效</li>
        </ul>

        <h3>🚫 屏蔽设置</h3>
        <h4>单聊屏蔽列表</h4>
        <ul>
            <li><strong>功能</strong>：指定不进行自动回复的用户昵称</li>
            <li><strong>使用场景</strong>：
                <ul>
                    <li>重要客户或领导的消息需要人工处理</li>
                    <li>家人朋友的消息不适合自动回复</li>
                    <li>特定同事的工作沟通</li>
                </ul>
            </li>
            <li><strong>操作方法</strong>：
                <ol>
                    <li>在输入框中输入用户昵称（必须与企业微信中显示的完全一致）</li>
                    <li>点击"添加"按钮</li>
                    <li>点击"保存屏蔽配置"</li>
                </ol>
            </li>
        </ul>

        <h4>群聊屏蔽列表</h4>
        <ul>
            <li><strong>功能</strong>：指定不进行自动回复的群聊名称</li>
            <li><strong>使用场景</strong>：
                <ul>
                    <li>重要工作群不适合自动回复</li>
                    <li>管理层讨论群</li>
                    <li>客户服务群</li>
                </ul>
            </li>
            <li><strong>操作方法</strong>：与单聊屏蔽相同</li>
        </ul>

        <h3>📁 配置文件</h3>
        <ul>
            <li><strong>文件位置</strong>：config-gui.json</li>
            <li><strong>自动保存</strong>：通过界面修改的配置会自动保存到文件</li>
            <li><strong>手动编辑</strong>：也可以直接编辑JSON文件（需要重启程序）</li>
        </ul>

        <div style="background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>⚠️ 重要提示</strong>：
            <ul>
                <li>用户昵称和群聊名称必须与企业微信中显示的完全一致</li>
                <li>注意大小写、空格、特殊字符等细节</li>
                <li>建议先测试确认昵称正确性</li>
            </ul>
        </div>
        """)

        layout.addWidget(text_edit)
        self.tab_widget.addTab(widget, "⚙️ 配置说明")

    def create_troubleshooting_tab(self):
        """创建故障排除标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        text_edit.setHtml("""
        <h2>🔧 故障排除</h2>

        <h3>❌ 常见问题</h3>

        <h4>Q: 点击"启动服务"后出现"获取数据失败，重试"错误？</h4>
        <p><strong>A:</strong> 这通常是企业微信客户端连接问题：</p>
        <ul>
            <li>确保企业微信客户端已启动并登录</li>
            <li>检查企业微信是否正常运行（可以手动发送消息测试）</li>
            <li>重启企业微信客户端</li>
            <li>检查是否安装了ntwork库：<code>pip install ntwork</code></li>
        </ul>

        <h4>Q: 设置了屏蔽但仍然自动回复？</h4>
        <p><strong>A:</strong> 检查以下几点：</p>
        <ul>
            <li>用户昵称是否与企业微信中显示的完全一致</li>
            <li>是否点击了"保存屏蔽配置"按钮</li>
            <li>查看日志是否有屏蔽相关的记录</li>
            <li>重新启动服务试试</li>
        </ul>

        <h4>Q: 倒计时结束后没有自动启动？</h4>
        <p><strong>A:</strong> 可能的原因：</p>
        <ul>
            <li>企业微信客户端未登录</li>
            <li>网络连接问题</li>
            <li>权限问题（尝试以管理员身份运行）</li>
        </ul>

        <h4>Q: 接收到消息但没有自动回复？</h4>
        <p><strong>A:</strong> 检查：</p>
        <ul>
            <li>是否设置了回复内容</li>
            <li>该用户/群聊是否在屏蔽列表中</li>
            <li>查看日志中的错误信息</li>
            <li>检查企业微信是否有消息发送限制</li>
        </ul>

        <h3>🔍 调试方法</h3>

        <h4>查看日志信息</h4>
        <ul>
            <li>运行界面的日志区域会显示详细的运行信息</li>
            <li>注意查看错误信息和警告</li>
            <li>正常情况下会显示"接收到新的消息"和"已自动回复"</li>
        </ul>

        <h4>检查统计信息</h4>
        <ul>
            <li>运行统计区域显示消息处理情况</li>
            <li>如果接收消息数增加但回复消息数不增加，说明回复功能有问题</li>
            <li>成功率可以帮助判断系统稳定性</li>
        </ul>

        <h4>测试步骤</h4>
        <ol>
            <li>先在测试群或与同事的私聊中测试</li>
            <li>发送简单的测试消息</li>
            <li>观察日志输出和统计变化</li>
            <li>确认功能正常后再正式使用</li>
        </ol>

        <div style="background-color: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>💡 提示</strong>：如果遇到无法解决的问题，可以重启程序或重启企业微信客户端。
        </div>
        """)

        layout.addWidget(text_edit)
        self.tab_widget.addTab(widget, "🔧 故障排除")

    def create_support_tab(self):
        """创建技术支持标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 标题
        title_label = QLabel("📞 技术支持")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2c3e50; margin: 20px;")
        scroll_layout.addWidget(title_label)

        # 支持信息
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(200)
        info_text.setHtml("""
        <div style="text-align: center; padding: 20px;">
            <h3 style="color: #3498db;">🎯 听闻远方有你</h3>
            <p style="font-size: 12pt; color: #2c3e50; margin: 15px 0;">
                如果您在使用过程中遇到问题，或者有任何建议和反馈，<br>
                欢迎联系我们的技术支持团队。我们将竭诚为您服务！
            </p>
            <p style="font-size: 11pt; color: #7f8c8d; margin: 10px 0;">
                <strong>联系方式请扫描下方二维码</strong>
            </p>
        </div>
        """)
        scroll_layout.addWidget(info_text)

        # 联系方式图片
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("""
            QLabel {
                border: 2px solid #3498db;
                border-radius: 10px;
                padding: 10px;
                background-color: white;
                margin: 10px;
            }
        """)

        # 尝试加载图片
        try:
            import os
            image_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "12.jpg")
            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 缩放图片到合适大小
                    scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    image_label.setPixmap(scaled_pixmap)
                else:
                    image_label.setText("📱 联系方式图片\n(12.jpg)")
                    image_label.setStyleSheet(image_label.styleSheet() + "font-size: 14pt; color: #7f8c8d;")
            else:
                image_label.setText("📱 联系方式图片\n(12.jpg 文件未找到)")
                image_label.setStyleSheet(image_label.styleSheet() + "font-size: 14pt; color: #e74c3c;")
        except Exception as e:
            image_label.setText(f"📱 联系方式图片\n(加载失败: {str(e)})")
            image_label.setStyleSheet(image_label.styleSheet() + "font-size: 12pt; color: #e74c3c;")

        image_label.setMinimumHeight(320)
        scroll_layout.addWidget(image_label)

        # 服务说明
        service_text = QTextEdit()
        service_text.setReadOnly(True)
        service_text.setMaximumHeight(150)
        service_text.setHtml("""
        <div style="padding: 15px;">
            <h4 style="color: #27ae60;">🛠️ 我们提供的服务</h4>
            <ul style="color: #2c3e50; line-height: 1.6;">
                <li><strong>功能使用指导</strong>：详细的操作步骤和使用技巧</li>
                <li><strong>问题故障排除</strong>：快速定位和解决技术问题</li>
                <li><strong>配置优化建议</strong>：根据使用场景提供最佳配置方案</li>
                <li><strong>功能定制需求</strong>：根据特殊需求提供定制化解决方案</li>
            </ul>

            <h4 style="color: #e67e22;">⏰ 服务时间</h4>
            <p style="color: #2c3e50;">工作日 9:00-18:00，我们会在24小时内回复您的咨询。</p>
        </div>
        """)
        scroll_layout.addWidget(service_text)

        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)

        layout.addWidget(scroll_area)
        self.tab_widget.addTab(widget, "📞 技术支持")
