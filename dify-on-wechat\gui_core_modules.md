# GUI版本核心模块依赖分析

## 必须保留的核心模块

### 1. 企业微信通道模块
- **文件**: `channel/wework/wework_channel.py`
- **依赖**: `channel/wework/wework_message.py`
- **功能**: 企业微信连接、消息接收、消息发送
- **状态**: 完整保留

### 2. Mock机器人模块
- **文件**: `bot/mock/mock_bot.py`
- **功能**: 固定文本自动回复
- **状态**: 完整保留

### 3. 基础架构模块
- **文件**: 
  - `channel/chat_channel.py` (基类)
  - `channel/chat_message.py` (消息基类)
  - `bot/bot.py` (Bot基类)
  - `bridge/context.py` (上下文)
  - `bridge/reply.py` (回复)
- **状态**: 完整保留

### 4. 配置系统
- **文件**: `config.py`
- **功能**: 配置加载和管理
- **状态**: 保留核心功能，简化配置项

### 5. 日志系统
- **文件**: `common/log.py`
- **功能**: 日志记录和格式化
- **状态**: 保留，需要集成到GUI

### 6. 工具模块
- **文件**: 
  - `common/singleton.py` (单例模式)
  - `common/expired_dict.py` (过期字典)
- **状态**: 保留

## 需要移除的模块

### 1. 其他平台通道
- `channel/wechat/` (微信)
- `channel/dingtalk/` (钉钉)
- `channel/feishu/` (飞书)
- `channel/wechatmp/` (微信公众号)
- `channel/wechatcom/` (企业微信应用)
- `channel/terminal/` (终端)
- `channel/web/` (Web)

### 2. AI机器人
- `bot/openai/` (OpenAI)
- `bot/chatgpt/` (ChatGPT)
- `bot/claude/` (Claude)
- `bot/moonshot/` (月之暗面)
- `bot/deepseek/` (DeepSeek)
- 等其他AI机器人

### 3. 插件系统
- `plugins/` 整个目录

### 4. 语音处理
- `voice/` 目录

### 5. 其他工具
- `translate/` (翻译)
- `dsl/` (DSL)

## 简化的依赖列表

### Python包依赖 (requirements-gui.txt)
```
# 基础依赖
requests>=2.28.2
chardet>=5.1.0
Pillow

# 企业微信SDK
ntwork  # 需要确认版本

# GUI框架
PyQt5>=5.15.0

# 日志和工具
# (使用Python内置模块)
```

### 核心文件结构
```
企业微信/dify-on-wechat/
├── gui/                    # GUI模块 (新增)
├── channel/
│   ├── wework/            # 企业微信通道 (保留)
│   ├── chat_channel.py    # 基类 (保留)
│   └── chat_message.py    # 消息基类 (保留)
├── bot/
│   ├── mock/              # Mock机器人 (保留)
│   └── bot.py             # Bot基类 (保留)
├── bridge/                # 桥接模块 (保留)
├── common/                # 公共模块 (保留核心部分)
├── config.py              # 配置系统 (简化)
├── config-gui.json        # GUI配置文件 (新增)
└── gui_app.py             # GUI入口 (新增)
```

## 配置简化

### 移除的配置项
- 所有AI相关配置
- 其他平台配置
- 插件配置
- 语音配置
- 图片识别配置

### 保留的配置项
- channel_type: "wework"
- model: "mock"
- auto_reply_text: 自动回复内容
- single_chat_prefix: 单聊触发前缀
- group_chat_prefix: 群聊触发前缀
- group_name_white_list: 群聊白名单
- debug: 调试模式

## 代码修改要点

### 1. 移除不必要的导入
- 移除其他平台的导入
- 移除AI机器人的导入
- 移除插件相关导入

### 2. 简化工厂类
- `channel_factory.py`: 只保留wework通道
- `bot_factory.py`: 只保留mock机器人

### 3. 配置加载优化
- 使用简化的配置文件
- 移除不必要的配置验证

这个分析为后续的代码简化提供了明确的指导。
