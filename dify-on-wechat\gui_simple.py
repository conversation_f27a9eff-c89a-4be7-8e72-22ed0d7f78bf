#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信自动回复系统 - 简化启动文件
用于PyInstaller打包
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """主函数"""
    try:
        # 导入PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("企业微信自动回复系统")
        app.setApplicationVersion("1.0.0")
        
        # 导入主窗口
        from gui.main_window import MainWindow
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        # 运行应用
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装PyQt5: pip install PyQt5")
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
