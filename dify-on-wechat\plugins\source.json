{"repo": {"sdwebui": {"url": "https://github.com/lanvent/plugin_sdwebui.git", "desc": "利用stable-diffusion画图的插件"}, "replicate": {"url": "https://github.com/lanvent/plugin_replicate.git", "desc": "利用replicate api画图的插件"}, "summary": {"url": "https://github.com/lanvent/plugin_summary.git", "desc": "总结聊天记录的插件"}, "Apilot": {"url": "https://github.com/6vision/Apilot.git", "desc": "通过api直接查询早报、热榜、快递、天气等实用信息的插件"}, "GroupInvitation": {"url": "https://github.com/dfldylan/GroupInvitation.git", "desc": "根据特定关键词自动邀请用户加入指定的群聊"}, "send_msg": {"url": "https://github.com/Isaac20231231/send_msg.git", "desc": "手动发送微信通知消息插件,通过api发送消息/微信命令发送消息，支持个人/群聊"}, "pictureChange": {"url": "https://github.com/Yanyutin753/pictureChange.git", "desc": "1. 支持百度AI和Stable Diffusion WebUI进行图像处理，提供多种模型选择，支持图生图、文生图自定义模板。2. 支持Suno音乐AI可将图像和文字转为音乐。3. 支持自定义模型进行文件、图片总结功能。4. 支持管理员控制群聊内容与参数和功能改变。"}, "Blackroom": {"url": "https://github.com/dividduang/blackroom.git", "desc": "小黑屋插件，被拉进小黑屋的人将不能使用@bot的功能的插件"}, "midjourney": {"url": "https://github.com/baojingyu/midjourney.git", "desc": "利用midjourney实现ai绘图的的插件"}, "solitaire": {"url": "https://github.com/Wang-<PERSON>chao/solitaire.git", "desc": "机器人微信接龙插件"}, "HighSpeedTicket": {"url": "https://github.com/He0607/HighSpeedTicket.git", "desc": "高铁（火车）票查询插件"}}}