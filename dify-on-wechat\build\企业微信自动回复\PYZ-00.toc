('E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\PYZ-00.pyz',
 [('PyQt5',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Miniconda3\\envs\\py38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Miniconda3\\envs\\py38\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Miniconda3\\envs\\py38\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Miniconda3\\envs\\py38\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Miniconda3\\envs\\py38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Miniconda3\\envs\\py38\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Miniconda3\\envs\\py38\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\Miniconda3\\envs\\py38\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Miniconda3\\envs\\py38\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Miniconda3\\envs\\py38\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Miniconda3\\envs\\py38\\lib\\calendar.py', 'PYMODULE'),
  ('common', '-', 'PYMODULE'),
  ('common.const',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\common\\const.py',
   'PYMODULE'),
  ('common.log',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\common\\log.py',
   'PYMODULE'),
  ('config', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\config.py', 'PYMODULE'),
  ('configparser',
   'D:\\Miniconda3\\envs\\py38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Miniconda3\\envs\\py38\\lib\\contextlib.py', 'PYMODULE'),
  ('copy', 'D:\\Miniconda3\\envs\\py38\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Miniconda3\\envs\\py38\\lib\\csv.py', 'PYMODULE'),
  ('datetime', 'D:\\Miniconda3\\envs\\py38\\lib\\datetime.py', 'PYMODULE'),
  ('dis', 'D:\\Miniconda3\\envs\\py38\\lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\Miniconda3\\envs\\py38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Miniconda3\\envs\\py38\\lib\\fnmatch.py', 'PYMODULE'),
  ('getopt', 'D:\\Miniconda3\\envs\\py38\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Miniconda3\\envs\\py38\\lib\\gettext.py', 'PYMODULE'),
  ('gui',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\__init__.py',
   'PYMODULE'),
  ('gui.config_loader',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\config_loader.py',
   'PYMODULE'),
  ('gui.donate_window',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\donate_window.py',
   'PYMODULE'),
  ('gui.help_window',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\help_window.py',
   'PYMODULE'),
  ('gui.main_window',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\main_window.py',
   'PYMODULE'),
  ('gui.managers',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\managers.py',
   'PYMODULE'),
  ('gui.system_tray',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\system_tray.py',
   'PYMODULE'),
  ('gzip', 'D:\\Miniconda3\\envs\\py38\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Miniconda3\\envs\\py38\\lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Miniconda3\\envs\\py38\\lib\\inspect.py', 'PYMODULE'),
  ('json', 'D:\\Miniconda3\\envs\\py38\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Miniconda3\\envs\\py38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Miniconda3\\envs\\py38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Miniconda3\\envs\\py38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Miniconda3\\envs\\py38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Miniconda3\\envs\\py38\\lib\\lzma.py', 'PYMODULE'),
  ('opcode', 'D:\\Miniconda3\\envs\\py38\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\Miniconda3\\envs\\py38\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\Miniconda3\\envs\\py38\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\Miniconda3\\envs\\py38\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Miniconda3\\envs\\py38\\lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'D:\\Miniconda3\\envs\\py38\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Miniconda3\\envs\\py38\\lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\Miniconda3\\envs\\py38\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Miniconda3\\envs\\py38\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\Miniconda3\\envs\\py38\\lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\Miniconda3\\envs\\py38\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Miniconda3\\envs\\py38\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Miniconda3\\envs\\py38\\lib\\socket.py', 'PYMODULE'),
  ('string', 'D:\\Miniconda3\\envs\\py38\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Miniconda3\\envs\\py38\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Miniconda3\\envs\\py38\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\Miniconda3\\envs\\py38\\lib\\tarfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Miniconda3\\envs\\py38\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Miniconda3\\envs\\py38\\lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\Miniconda3\\envs\\py38\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Miniconda3\\envs\\py38\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Miniconda3\\envs\\py38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'D:\\Miniconda3\\envs\\py38\\lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'D:\\Miniconda3\\envs\\py38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Miniconda3\\envs\\py38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('uu', 'D:\\Miniconda3\\envs\\py38\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'D:\\Miniconda3\\envs\\py38\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Miniconda3\\envs\\py38\\lib\\zipimport.py', 'PYMODULE')])
