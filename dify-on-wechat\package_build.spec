# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件列表
datas = []

# 添加图标和图片文件
resource_files = ['2.ico', '12.jpg', '33.png', 'config-gui.json']
for file in resource_files:
    file_path = os.path.join(project_root, file)
    if os.path.exists(file_path):
        datas.append((file_path, '.'))

# 添加ntwork wheel文件
ntwork_wheel = os.path.join(project_root, 'ntwork-0.1.3-cp310-cp310-win_amd64.whl')
if os.path.exists(ntwork_wheel):
    datas.append((ntwork_wheel, '.'))

# 添加GUI模块
gui_modules = ['gui']
for module in gui_modules:
    module_path = os.path.join(project_root, module)
    if os.path.exists(module_path):
        for root, dirs, files in os.walk(module_path):
            for file in files:
                if file.endswith('.py'):
                    src_file = os.path.join(root, file)
                    rel_path = os.path.relpath(root, project_root)
                    datas.append((src_file, rel_path))

# 隐藏导入模块
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'gui.main_window',
    'gui.help_window',
    'gui.donate_window',
    'gui.managers',
    'gui.system_tray',
    'gui.config_loader',
    'bot.mock.mock_bot',
    'channel.wework.wework_channel',
    'bridge.context',
    'bridge.reply',
    'common.log',
    'common.utils',
    'config',
    'requests',
    'json',
    'threading',
    'queue',
    'time',
    'datetime',
    'os',
    'sys'
]

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy', 
        'scipy',
        'pandas',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'sklearn',
        'jupyter',
        'notebook',
        'IPython',
        'sphinx',
        'pytest',
        'setuptools',
        'tkinter',
        'pygame'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 窗口模式，不显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, '2.ico') if os.path.exists(os.path.join(project_root, '2.ico')) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复系统',
)
