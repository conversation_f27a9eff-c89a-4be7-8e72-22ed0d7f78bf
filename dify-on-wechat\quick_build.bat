@echo off
chcp 65001 > nul
title 企业微信自动回复系统 - 快速打包工具

echo ============================================================
echo 🎯 企业微信自动回复系统 - 快速打包工具
echo ============================================================
echo.

echo 🔍 检查环境...
python --version
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ Python 环境正常
echo.

echo 🧹 清理之前的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec"
echo ✅ 清理完成
echo.

echo 📦 开始打包...
pyinstaller --onedir --windowed --name="企业微信自动回复系统" --icon=2.ico gui_app.py

if errorlevel 1 (
    echo ❌ 打包失败，请检查错误信息
    pause
    exit /b 1
)

echo ✅ 打包完成
echo.

echo 📁 复制必要文件...
set "DIST_DIR=dist\企业微信自动回复系统"

if exist "2.ico" (
    copy "2.ico" "%DIST_DIR%\"
    echo ✅ 复制图标文件: 2.ico
) else (
    echo ⚠️ 图标文件不存在: 2.ico
)

if exist "12.jpg" (
    copy "12.jpg" "%DIST_DIR%\"
    echo ✅ 复制联系方式: 12.jpg
) else (
    echo ⚠️ 联系方式图片不存在: 12.jpg
)

if exist "33.png" (
    copy "33.png" "%DIST_DIR%\"
    echo ✅ 复制请我喝茶: 33.png
) else (
    echo ⚠️ 请我喝茶图片不存在: 33.png
)

if exist "config-gui.json" (
    copy "config-gui.json" "%DIST_DIR%\"
    echo ✅ 复制配置文件: config-gui.json
) else (
    echo ⚠️ 配置文件不存在: config-gui.json
)

if exist "README.md" (
    copy "README.md" "%DIST_DIR%\"
    echo ✅ 复制说明文档: README.md
)

if exist "GUI使用说明.md" (
    copy "GUI使用说明.md" "%DIST_DIR%\"
    echo ✅ 复制使用说明: GUI使用说明.md
)

if exist "打包说明.md" (
    copy "打包说明.md" "%DIST_DIR%\"
    echo ✅ 复制打包说明: 打包说明.md
)

echo.
echo 📝 创建启动脚本...
(
echo @echo off
echo chcp 65001 ^> nul
echo title 企业微信自动回复系统
echo echo 🚀 正在启动企业微信自动回复系统...
echo echo.
echo echo 💡 使用提示：
echo echo    1. 确保企业微信客户端已启动并登录
echo echo    2. 首次使用请先配置自动回复内容
echo echo    3. 如有问题请查看使用说明文档
echo echo.
echo echo ⏳ 正在加载程序，请稍候...
echo echo.
echo "企业微信自动回复系统.exe"
echo pause
) > "%DIST_DIR%\启动程序.bat"

echo ✅ 创建启动脚本: 启动程序.bat
echo.

echo ============================================================
echo 🎉 打包完成！
echo ============================================================
echo 📁 输出目录: %DIST_DIR%
echo 🚀 启动文件: 企业微信自动回复系统.exe
echo 📝 启动脚本: 启动程序.bat
echo.
echo 💡 使用说明:
echo    1. 将整个 "企业微信自动回复系统" 文件夹复制到目标电脑
echo    2. 双击 "启动程序.bat" 或 "企业微信自动回复系统.exe" 启动程序
echo    3. 确保目标电脑已安装企业微信客户端
echo.
echo 📋 测试清单:
echo    □ exe文件能正常启动
echo    □ GUI界面显示正常
echo    □ 配置功能正常
echo    □ 帮助窗口能打开
echo    □ 请我喝茶窗口能打开
echo    □ 图片文件正常显示
echo.
echo ============================================================

pause
