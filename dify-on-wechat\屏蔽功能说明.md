# 企业微信自动回复系统 - 屏蔽功能详细说明

## 🎯 功能概述

屏蔽功能允许您指定某些用户或群聊不进行自动回复，这对于以下场景非常有用：
- 重要客户或领导的消息需要人工处理
- 某些工作群聊不适合自动回复
- 避免对特定联系人造成困扰

## 🛠️ 功能特性

### ✅ 支持的屏蔽类型
1. **单聊屏蔽**: 屏蔽指定用户的私聊消息
2. **群聊屏蔽**: 屏蔽指定群聊中的消息

### ✅ 实时生效
- 配置保存后立即生效，无需重启服务
- 支持运行时动态添加和删除屏蔽项

### ✅ 智能匹配
- 基于用户昵称和群聊名称进行精确匹配
- 支持中文、英文等各种字符

## 📋 使用方法

### 1. 打开屏蔽设置
1. 启动GUI应用程序
2. 在配置设置区域，点击"屏蔽设置"标签页
3. 您会看到两个区域：单聊屏蔽列表和群聊屏蔽列表

### 2. 添加单聊屏蔽
```
步骤：
1. 在"单聊屏蔽列表"区域的输入框中输入用户昵称
2. 点击"添加"按钮
3. 用户昵称会出现在上方的列表中
4. 点击"保存屏蔽配置"按钮保存设置
```

**示例**:
- 输入: `张总`
- 结果: 来自"张总"的私聊消息将不会触发自动回复

### 3. 添加群聊屏蔽
```
步骤：
1. 在"群聊屏蔽列表"区域的输入框中输入群聊名称
2. 点击"添加"按钮
3. 群聊名称会出现在上方的列表中
4. 点击"保存屏蔽配置"按钮保存设置
```

**示例**:
- 输入: `管理层讨论群`
- 结果: 来自"管理层讨论群"的消息将不会触发自动回复

### 4. 删除屏蔽项
```
步骤：
1. 在屏蔽列表中选中要删除的项目
2. 点击"删除选中"按钮
3. 项目从列表中移除
4. 点击"保存屏蔽配置"按钮保存更改
```

## 🔧 技术实现

### 匹配机制
系统会检查以下信息来判断是否屏蔽：

**单聊消息**:
- 检查发送者的昵称是否在单聊屏蔽列表中
- 精确匹配用户昵称

**群聊消息**:
- 检查群聊名称是否在群聊屏蔽列表中
- 精确匹配群聊名称

### 配置存储
屏蔽配置存储在 `config-gui.json` 文件中：
```json
{
  "single_chat_block_list": ["张总", "李经理"],
  "group_chat_block_list": ["管理层讨论群", "董事会群"]
}
```

### 日志记录
当消息被屏蔽时，系统会在日志中记录：
```
[MockBot] 单聊用户 '张总' 在屏蔽列表中
[MockBot] 群聊 '管理层讨论群' 在屏蔽列表中
[MockBot] 消息已被屏蔽，不进行自动回复
```

## 📝 使用建议

### 1. 昵称准确性
- 确保输入的用户昵称与企业微信中显示的完全一致
- 注意大小写、空格、特殊字符等细节

### 2. 群聊名称
- 使用群聊的完整名称
- 如果群聊名称经常变更，需要及时更新屏蔽列表

### 3. 测试验证
- 添加屏蔽后，建议先测试确认是否生效
- 可以通过日志查看屏蔽状态

### 4. 定期维护
- 定期检查屏蔽列表，删除不再需要的项目
- 根据组织架构变化更新屏蔽列表

## ⚠️ 注意事项

### 1. 精确匹配
- 屏蔽功能使用精确匹配，昵称必须完全一致
- 如果昵称包含特殊字符，需要完整输入

### 2. 实时生效
- 配置保存后立即生效
- 正在运行的服务会立即应用新的屏蔽规则

### 3. 优先级
- 屏蔽规则优先级高于自动回复
- 被屏蔽的消息不会触发任何自动回复

### 4. 日志监控
- 建议通过日志监控屏蔽功能的工作状态
- 如果屏蔽不生效，检查昵称是否准确

## 🔍 故障排除

### Q: 添加了屏蔽但仍然自动回复？
A: 检查以下几点：
1. 昵称是否与企业微信中显示的完全一致
2. 是否点击了"保存屏蔽配置"按钮
3. 查看日志是否有屏蔽相关的记录

### Q: 如何确认屏蔽是否生效？
A: 
1. 查看GUI日志区域，屏蔽时会显示相关信息
2. 发送测试消息，观察是否有自动回复
3. 检查配置文件中是否正确保存了屏蔽列表

### Q: 可以屏蔽多少个用户/群聊？
A: 
- 理论上没有数量限制
- 建议合理控制数量，避免配置过于复杂

### Q: 屏蔽配置会影响性能吗？
A: 
- 屏蔽检查非常轻量，对性能影响微乎其微
- 即使有大量屏蔽项，也不会明显影响响应速度

## 📊 配置示例

### 典型企业场景
```json
{
  "single_chat_block_list": [
    "总经理",
    "人事总监", 
    "财务经理",
    "重要客户A",
    "重要客户B"
  ],
  "group_chat_block_list": [
    "高管会议群",
    "董事会讨论",
    "重要项目群",
    "客户服务群",
    "紧急事务处理群"
  ]
}
```

### 小团队场景
```json
{
  "single_chat_block_list": [
    "老板",
    "直属领导"
  ],
  "group_chat_block_list": [
    "管理层群",
    "核心团队群"
  ]
}
```

---

**屏蔽功能让您的自动回复更加智能和人性化！** 🎉
