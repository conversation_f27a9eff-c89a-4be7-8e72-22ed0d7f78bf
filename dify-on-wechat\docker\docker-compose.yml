version: '2.0'
services:
  dify-on-wechat:
    # 对镜像地址的说明 
    # docker hub 仓库国内无法访问, 可以使用ACR阿里云容器仓库，但由于我使用的是免费个人版仓库不保证可用性

    # 对tag的说明 
    # tag为 master 表示是主分支的镜像，最新版本，功能不稳定
    # tag为 latest 表示是最新的release镜像，相对稳定(个人没有精力全面测试)
    # tag为 arm64  表示镜像为arm64架构镜像，其他为amd64架构镜像
    
    # image: hanfangyuan/dify-on-wechat:latest # docker hub
    # image: hanfangyuan/dify-on-wechat:arm64  # docker hub arm64
    image: registry.cn-hangzhou.aliyuncs.com/hanfangyuan/dify-on-wechat:latest # acr
    # image: registry.cn-hangzhou.aliyuncs.com/hanfangyuan/dify-on-wechat:arm64 # acr arm64
    user: root
    restart: always
    pull_policy: always
    container_name: dify-on-wechat
    security_opt:
      - seccomp:unconfined
    
    # 注意环境变量配置优先级高于config.json配置，环境变量无法热更新，推荐使用config.json配置
    environment:
      # dify 相关配置
      # DIFY_API_BASE: 'https://api.dify.ai/v1'
      # DIFY_API_KEY: 'app-xx'
      # DIFY_APP_TYPE: 'chatbot'                    # dify助手类型 chatbot(对应聊天助手)/agent(对应Agent)/workflow(对应工作流)，默认为chatbot
      # MODEL: 'dify'                               # 默认为 dify,默认使用coze 则填写 coze,也可以在启动后使用godcmd=>"#model coze"进行模型热切换(必须先认证管理员权限)
      # DIFY_CONVERSATION_MAX_MESSAGES: '5'       # dify目前不支持设置历史消息长度，暂时使用超过最大消息数清空会话的策略，缺点是没有滑动窗口，会突然丢失历史消息，当设置的值小于等于0，则不限制历史消息长度
      
      # 会话相关配置
      # EXPIRES_IN_SECONDS: '3600'                  # dify会话过期时间，单位秒，默认3600秒
      # SINGLE_CHAT_PREFIX: '[""]'                  # 私聊前缀，配置示例 '["bot", "ai"]'
      # SINGLE_CHAT_REPLY_PREFIX: '""'              # 单聊回复前缀，配置示例 '"bot"' 只支持单个字符串，请勿配置成列表形式
      # GROUP_CHAT_PREFIX: '["@bot"]'
      # GROUP_NAME_WHITE_LIST: '["ALL_GROUP"]'
      TZ: 'Asia/Shanghai'

      # web_ui 相关配置，只支持wx 和 gewechat channel_tpye，默认不开启，取消注释下方配置可开启web_ui功能
      # DIFY_ON_WECHAT_EXEC: 'python web_ui.py'
      # WEB_UI_PORT: '7860'
      # WEB_UI_USERNAME: 'dow'                    
      # WEB_UI_PASSWORD: 'dify-on-wechat'         # 务必保证修改默认的用户名和密码
      # 图片理解功能
      # IMAGE_RECOGNITION: 'false'                # 是否开启图片理解功能，默认为false

      # coze 相关配置
      # COZE_API_BASE: 'https://api.coze.cn/open_api/v2'
      # COZE_API_KEY: 'xxx'
      # COZE_BOT_ID: 'xxx'
      
      # 更多配置请查看 config.py
    ports:
      - "7860:7860"
      - "9919:9919" # gewechat 回调服务端口

    # 挂载配置和插件目录，防止删除容器后丢失数据，注意需要先复制config-template.json为config.json
    volumes:
      # 如果想要实现在宿主机修改config.json然后通过 #reconf 热更新配置
      # 请执行 chmod 666 config.json，增加容器对宿主机的config.json文件修改权限
      # 注意环境变量配置优先级高于config.json配置
      - ../config.json:/app/config.json
      - ../plugins:/app/plugins
