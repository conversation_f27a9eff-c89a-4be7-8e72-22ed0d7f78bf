@echo off
chcp 65001 > nul
title 企业微信自动回复系统

echo ============================================================
echo 🚀 企业微信自动回复系统
echo ============================================================
echo.
echo 💡 使用提示：
echo    1. 确保企业微信客户端已启动并登录
echo    2. 首次使用请先配置自动回复内容
echo    3. 如有问题请查看使用说明文档
echo.
echo ⏳ 正在启动程序，请稍候...
echo.

cd /d "%~dp0"

REM 检查Python环境
if not exist "python\python.exe" (
    echo ❌ Python环境未找到！
    echo 请确保python目录存在且包含python.exe
    echo.
    pause
    exit /b 1
)

REM 检查应用程序
if not exist "app\gui_app.py" (
    echo ❌ 应用程序未找到！
    echo 请确保app目录存在且包含gui_app.py
    echo.
    pause
    exit /b 1
)

REM 启动程序
python\python.exe app\gui_app.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，可能的原因：
    echo    1. Python环境配置问题
    echo    2. 缺少必要依赖
    echo    3. 企业微信客户端未运行
    echo.
    echo 💡 解决方法：
    echo    1. 运行"安装依赖.bat"重新安装依赖
    echo    2. 确保企业微信客户端正常运行
    echo    3. 查看使用说明获取更多帮助
    echo.
    pause
)
