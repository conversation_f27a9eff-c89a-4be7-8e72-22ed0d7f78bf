# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui_app.py'],
    pathex=['E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\bot', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\channel', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\bridge', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\common'],
    binaries=[],
    datas=[('E:\\Desktop\\123\\企业微信\\dify-on-wechat\\2.ico', '.'), ('E:\\Desktop\\123\\企业微信\\dify-on-wechat\\12.jpg', '.'), ('E:\\Desktop\\123\\企业微信\\dify-on-wechat\\33.png', '.')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['E:\\Desktop\\OCR识别\\wechat_ocr_ui\\2.ico'],
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复',
)
