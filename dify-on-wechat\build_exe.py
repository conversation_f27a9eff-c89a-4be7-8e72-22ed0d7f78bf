#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信自动回复系统打包脚本
使用PyInstaller将项目打包成单目录exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查打包依赖"""
    print("🔍 检查打包依赖...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller 安装完成")
    
    try:
        import PyQt5
        print(f"✅ PyQt5 已安装")
    except ImportError:
        print("❌ PyQt5 未安装，请先安装: pip install PyQt5")
        return False
    
    return True

def clean_build():
    """清理之前的构建文件"""
    print("🧹 清理之前的构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['*.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ 删除目录: {dir_name}")
    
    # 清理__pycache__目录
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                pycache_path = os.path.join(root, dir_name)
                shutil.rmtree(pycache_path)
                print(f"✅ 删除缓存: {pycache_path}")

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")

    try:
        # 使用简化的PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onedir",  # 单目录模式
            "--windowed",  # 无控制台窗口
            "--name=企业微信自动回复系统",
            "--icon=2.ico",
            "--add-data=2.ico;.",
            "--add-data=12.jpg;.",
            "--add-data=33.png;.",
            "--add-data=config-gui.json;.",
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtGui",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=gui.main_window",
            "--hidden-import=gui.help_window",
            "--hidden-import=gui.donate_window",
            "--hidden-import=gui.managers",
            "--hidden-import=bot.mock.mock_bot",
            "--hidden-import=channel.wework.wework_channel",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            "--exclude-module=scipy",
            "--exclude-module=pandas",
            "--exclude-module=tkinter",
            "gui_app.py"
        ]

        print(f"📦 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True)

        print("✅ 构建成功！")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到dist目录"""
    print("📁 复制额外文件...")
    
    dist_dir = "dist/企业微信自动回复系统"
    if not os.path.exists(dist_dir):
        print(f"❌ 找不到dist目录: {dist_dir}")
        return False
    
    # 需要复制的文件
    files_to_copy = [
        "README.md",
        "GUI使用说明.md",
        "屏蔽功能说明.md",
        "config-template.json"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            dst_path = os.path.join(dist_dir, file_name)
            shutil.copy2(file_name, dst_path)
            print(f"✅ 复制文件: {file_name}")
    
    # 创建tmp目录
    tmp_dir = os.path.join(dist_dir, "tmp")
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)
        print(f"✅ 创建目录: tmp")
    
    return True

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    dist_dir = "dist/企业微信自动回复系统"
    
    # 创建批处理启动脚本
    bat_content = """@echo off
chcp 65001 > nul
title 企业微信自动回复系统
echo 🚀 正在启动企业微信自动回复系统...
echo.
echo 💡 使用提示：
echo    1. 确保企业微信客户端已启动并登录
echo    2. 首次使用请先配置自动回复内容
echo    3. 如有问题请查看使用说明文档
echo.
echo ⏳ 正在加载程序，请稍候...
echo.
"企业微信自动回复系统.exe"
pause
"""
    
    bat_path = os.path.join(dist_dir, "启动程序.bat")
    with open(bat_path, 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    print(f"✅ 创建启动脚本: 启动程序.bat")
    
    return True

def get_dist_size():
    """获取打包后的大小"""
    dist_dir = "dist/企业微信自动回复系统"
    if not os.path.exists(dist_dir):
        return "未知"
    
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(dist_dir):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            total_size += os.path.getsize(filepath)
    
    # 转换为MB
    size_mb = total_size / (1024 * 1024)
    return f"{size_mb:.1f} MB"

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 企业微信自动回复系统 - 打包工具")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("gui_app.py"):
        print("❌ 请在项目根目录下运行此脚本")
        return False
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 清理构建文件
    clean_build()
    
    # 构建exe
    if not build_exe():
        return False
    
    # 复制额外文件
    if not copy_additional_files():
        return False
    
    # 创建启动脚本
    if not create_startup_script():
        return False
    
    # 显示结果
    dist_size = get_dist_size()
    print("\n" + "=" * 60)
    print("🎉 打包完成！")
    print("=" * 60)
    print(f"📁 输出目录: dist/企业微信自动回复系统")
    print(f"📦 打包大小: {dist_size}")
    print(f"🚀 启动文件: 企业微信自动回复系统.exe")
    print(f"📝 启动脚本: 启动程序.bat")
    print("\n💡 使用说明:")
    print("   1. 将整个 '企业微信自动回复系统' 文件夹复制到目标电脑")
    print("   2. 双击 '启动程序.bat' 或 '企业微信自动回复系统.exe' 启动程序")
    print("   3. 确保目标电脑已安装企业微信客户端")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
