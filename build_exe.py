#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业微信自动回复系统打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理spec文件生成的临时文件
    for file in Path('.').glob('*.spec'):
        if file.name != '企业微信自动回复系统.spec':
            file.unlink()

def create_simple_spec():
    """创建简化的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os

project_root = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=[
        ('config-template.json', '.'),
        ('2.ico', '.'),
        ('12.jpg', '.'),
        ('33.png', '.'),
        ('gui', 'gui'),
        ('common', 'common'),
        ('bridge', 'bridge'),
        ('channel', 'channel'),
        ('bot', 'bot'),
        ('translate', 'translate'),
        ('voice', 'voice'),
        ('plugins', 'plugins'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'gui.main_window',
        'gui.config_loader',
        'gui.controllers',
        'gui.managers',
        'gui.system_tray',
        'config',
        'common.log',
        'common.singleton',
        'bridge.bridge',
        'channel.wework.wework_channel',
        'bot.mock.mock_bot',
        'translate.factory',
        'voice.factory',
        'plugins.plugin_manager',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy', 
        'pandas',
        'scipy',
        'PIL',
        'cv2'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='2.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复系统',
)
'''
    
    with open('simple.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    return 'simple.spec'

def build_exe():
    """执行打包过程"""
    print("开始打包企业微信自动回复系统...")
    
    # 清理构建目录
    clean_build_dirs()
    
    # 创建简化的spec文件
    spec_file = create_simple_spec()
    
    try:
        # 执行PyInstaller命令
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            spec_file
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        
        print("✅ 打包成功!")
        print(f"输出目录: {os.path.abspath('dist')}")
        
        # 检查生成的文件
        dist_dir = Path('dist/企业微信自动回复系统')
        if dist_dir.exists():
            exe_file = dist_dir / '企业微信自动回复系统.exe'
            if exe_file.exists():
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"可执行文件: {exe_file}")
                print(f"文件大小: {size_mb:.1f} MB")
            else:
                print("⚠️ 未找到可执行文件")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def main():
    """主函数"""
    print("企业微信自动回复系统 - 打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('gui_app.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False
    
    # 检查PyInstaller
    try:
        subprocess.run(['pyinstaller', '--version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 错误: 未安装PyInstaller，请运行: pip install pyinstaller")
        return False
    
    # 执行打包
    success = build_exe()
    
    if success:
        print("\n🎉 打包完成!")
        print("可执行文件位于: dist/企业微信自动回复系统/企业微信自动回复系统.exe")
        print("\n使用说明:")
        print("1. 将整个 dist/企业微信自动回复系统 文件夹复制到目标机器")
        print("2. 双击运行 企业微信自动回复系统.exe")
        print("3. 首次运行时请配置相关参数")
    else:
        print("\n❌ 打包失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
