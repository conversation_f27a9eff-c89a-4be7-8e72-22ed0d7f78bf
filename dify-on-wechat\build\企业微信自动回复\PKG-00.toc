('E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\企业微信自动回复.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('gui_app',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui_app.py',
   'PYSOURCE')],
 'python38.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
