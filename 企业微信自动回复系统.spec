# -*- mode: python ; coding: utf-8 -*-

import os

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=[
        ('config-template.json', '.'),
        ('2.ico', '.'),
        ('12.jpg', '.'),
        ('33.png', '.'),
        ('gui', 'gui'),
        ('common', 'common'),
        ('bridge', 'bridge'),
        ('channel', 'channel'),
        ('bot', 'bot'),
        ('translate', 'translate'),
        ('voice', 'voice'),
        ('plugins', 'plugins'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'gui.main_window',
        'gui.config_loader',
        'gui.controllers',
        'gui.managers',
        'gui.system_tray',
        'gui.help_window',
        'gui.donate_window',
        'config',
        'common.log',
        'common.singleton',
        'common.utils',
        'common.const',
        'common.sorted_dict',
        'bridge.bridge',
        'bridge.context',
        'bridge.reply',
        'channel.wework.wework_channel',
        'bot.mock.mock_bot',
        'bot.bot_factory',
        'translate.factory',
        'voice.factory',
        'plugins.plugin_manager',
        'plugins.event',
        'plugins.plugin',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
    ],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='2.ico',
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复系统',
)
