# encoding:utf-8
"""
测试屏蔽配置的保存和加载
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """测试配置功能"""
    print("=== 测试屏蔽配置功能 ===")
    
    try:
        # 导入配置管理器
        from gui.managers import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 测试添加屏蔽项
        print("\n1. 添加测试屏蔽项...")
        config_manager.set_config("single_chat_block_list", ["尚二松", "测试用户"])
        config_manager.set_config("group_chat_block_list", ["测试群聊"])
        
        # 保存配置
        print("2. 保存配置...")
        if config_manager.save_config():
            print("✅ 配置保存成功")
        else:
            print("❌ 配置保存失败")
            return
        
        # 重新加载配置
        print("3. 重新加载配置...")
        config_manager2 = ConfigManager()
        
        # 检查配置是否正确加载
        single_blocks = config_manager2.get_config("single_chat_block_list", [])
        group_blocks = config_manager2.get_config("group_chat_block_list", [])
        
        print(f"单聊屏蔽列表: {single_blocks}")
        print(f"群聊屏蔽列表: {group_blocks}")
        
        # 测试全局配置
        print("\n4. 测试全局配置...")
        from config import conf
        global_single_blocks = conf().get("single_chat_block_list", [])
        global_group_blocks = conf().get("group_chat_block_list", [])
        
        print(f"全局单聊屏蔽列表: {global_single_blocks}")
        print(f"全局群聊屏蔽列表: {global_group_blocks}")
        
        # 测试MockBot屏蔽检查
        print("\n5. 测试MockBot屏蔽检查...")
        from bot.mock.mock_bot import MockBot
        from bridge.context import Context, ContextType
        
        # 创建模拟消息对象
        class MockMessage:
            def __init__(self, from_user_nickname, is_group=False):
                self.from_user_nickname = from_user_nickname
                self.is_group = is_group
                self.other_user_nickname = from_user_nickname if is_group else None
        
        # 创建模拟上下文
        mock_msg = MockMessage("尚二松", False)
        context = Context(ContextType.TEXT, "测试消息")
        context.kwargs = {
            'msg': mock_msg,
            'isgroup': False
        }
        
        # 测试屏蔽检查
        bot = MockBot()
        should_block = bot._should_block_message(context)
        print(f"屏蔽检查结果: {should_block}")
        
        if should_block:
            print("✅ 屏蔽功能正常工作")
        else:
            print("❌ 屏蔽功能未生效")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config()
