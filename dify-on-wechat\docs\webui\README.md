
春节假期，在 [v0.1.23](https://github.com/hanfangyuan4396/dify-on-wechat/releases/tag/0.1.23)
 版本，优化了gradio web ui，补一下相关文档。

## webui启动方式

- 源码：python web_ui.py
-  docker: 可在docker-compose.yml 中取消注释环境变量 DIFY_ON_WECHAT_EXEC: 'python web_ui.py'

## 访问方式

-  访问 http://服务器ip:7860 ，输入默认账号密码 dow/dify-on-wechat 。记得检查防火墙是否开放7860端口。

## 功能

- 登录gewechat微信
- 退出gewechat微信
- 查看机器人状态
- 重启服务

## 新版ui截图

![image](https://github.com/user-attachments/assets/5623da5b-d740-4aa1-b267-bf71591488b6)
![image](https://github.com/user-attachments/assets/6735e4e4-bc6d-4b8a-9854-a3dcf877e3eb)
![image](https://github.com/user-attachments/assets/92dc5488-440f-4deb-9826-ac4cc81fa868)
![image](https://github.com/user-attachments/assets/e008b2b4-0b9c-497e-9ab5-a98ea8d85481)
