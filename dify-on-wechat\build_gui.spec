# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 简化的隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'gui.main_window',
    'gui.help_window',
    'gui.donate_window',
    'gui.managers',
    'gui.system_tray',
    'bot.mock.mock_bot',
    'channel.wework.wework_channel',
    'bridge.context',
    'bridge.reply',
    'common.log',
    'config'
]

# 数据文件
datas = []

# 添加图标和图片文件
icon_files = ['2.ico', '12.jpg', '33.png']
for icon_file in icon_files:
    icon_path = os.path.join(project_root, icon_file)
    if os.path.exists(icon_path):
        datas.append((icon_path, '.'))

# 添加配置文件
config_files = ['config-gui.json', 'config-template.json']
for config_file in config_files:
    config_path = os.path.join(project_root, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib', 'numpy', 'scipy', 'pandas', 'PIL', 'cv2',
        'tensorflow', 'torch', 'sklearn', 'jupyter', 'notebook',
        'IPython', 'sphinx', 'pytest', 'setuptools', 'tkinter'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 设置为False隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, '2.ico') if os.path.exists(os.path.join(project_root, '2.ico')) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复系统',
)
