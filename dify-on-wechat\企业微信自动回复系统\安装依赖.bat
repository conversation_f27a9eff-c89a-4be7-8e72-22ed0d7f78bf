@echo off
chcp 65001 > nul
title 安装依赖

echo ============================================================
echo 🔧 企业微信自动回复系统 - 依赖安装
echo ============================================================
echo.

cd /d "%~dp0"

REM 检查Python环境
if not exist "python\python.exe" (
    echo ❌ Python环境未找到！
    echo 请先下载并解压Python嵌入式版本到python目录
    echo.
    pause
    exit /b 1
)

echo 📦 正在安装程序依赖，请稍候...
echo.

echo 🔧 配置pip...
python\python.exe -m ensurepip --default-pip
if errorlevel 1 (
    echo ⚠️ pip配置可能有问题，继续尝试安装依赖...
)

echo.
echo 📦 安装PyQt5...
python\python.exe -m pip install PyQt5
if errorlevel 1 (
    echo ❌ PyQt5安装失败
    pause
    exit /b 1
)

echo 📦 安装requests...
python\python.exe -m pip install requests
if errorlevel 1 (
    echo ❌ requests安装失败
    pause
    exit /b 1
)

echo 📦 安装chardet...
python\python.exe -m pip install chardet
if errorlevel 1 (
    echo ❌ chardet安装失败
    pause
    exit /b 1
)

echo 📦 安装ntwork...
if exist "app\ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    python\python.exe -m pip install app\ntwork-0.1.3-cp310-cp310-win_amd64.whl
    if errorlevel 1 (
        echo ⚠️ ntwork安装失败，但程序可能仍可正常运行
    )
) else (
    echo ⚠️ ntwork wheel文件未找到，跳过安装
)

echo.
echo ✅ 依赖安装完成！
echo 现在可以运行"启动程序.bat"启动应用程序
echo.
pause
