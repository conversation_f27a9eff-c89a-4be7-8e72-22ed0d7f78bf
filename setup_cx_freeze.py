#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业微信自动回复系统 - cx_Freeze打包配置
"""

import sys
import os
from cx_Freeze import setup, Executable

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))

# 包含的文件和目录
include_files = [
    ("config-template.json", "config-template.json"),
    ("2.ico", "2.ico"),
    ("12.jpg", "12.jpg"),
    ("33.png", "33.png"),
    ("gui", "gui"),
    ("common", "common"),
    ("bridge", "bridge"),
    ("channel", "channel"),
    ("bot", "bot"),
    ("translate", "translate"),
    ("voice", "voice"),
    ("plugins", "plugins"),
]

# 需要包含的包
packages = [
    "PyQt5.QtCore",
    "PyQt5.QtGui",
    "PyQt5.QtWidgets",
    "requests",
    "json",
    "threading",
    "queue",
    "datetime",
    "logging",
    "configparser",
    "sqlite3",
    "urllib",
    "http",
    "ssl",
    "socket"
]

# 需要排除的包
excludes = [
    "tkinter",
    "matplotlib",
    "numpy",
    "pandas",
    "scipy",
    "PIL",
    "cv2",
    "tensorflow",
    "torch",
    "sklearn",
    "jupyter",
    "IPython",
    "notebook",
    "sphinx",
    "pytest",
    "setuptools",
    "distutils"
]

# 构建选项
build_exe_options = {
    "packages": packages,
    "excludes": excludes,
    "include_files": include_files,
    "optimize": 2,
    "build_exe": "dist/企业微信自动回复系统",
}

# 可执行文件配置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 隐藏控制台窗口

executable = Executable(
    script="gui_app.py",
    base=base,
    target_name="企业微信自动回复系统.exe",
    icon="2.ico",
    copyright="企业微信自动回复系统",
)

# 设置信息
setup(
    name="企业微信自动回复系统",
    version="1.0.0",
    description="基于企业微信的智能自动回复系统",
    author="开发者",
    options={"build_exe": build_exe_options},
    executables=[executable],
)
