# encoding:utf-8

from bot.bot import Bot
from bridge.context import ContextType
from bridge.reply import Reply, ReplyType
from common.log import logger
from config import conf

# 全局统计管理器
class StatsManager:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.gui_window = None
        return cls._instance

    def set_gui_window(self, window):
        self.gui_window = window

    def increment_received(self):
        if self.gui_window:
            self.gui_window.increment_received_messages()

    def increment_replied(self):
        if self.gui_window:
            self.gui_window.increment_replied_messages()

    def increment_failed(self):
        if self.gui_window:
            self.gui_window.increment_failed_messages()

    def log_message(self, message):
        """直接发送消息到GUI日志"""
        if self.gui_window:
            self.gui_window.append_log(message)

# 全局统计管理器实例
stats_manager = StatsManager()


class MockBot(Bot):
    def __init__(self):
        super().__init__()
        # 不再缓存回复文本，每次都从配置中读取

    def reply(self, query, context=None):
        """
        简单的固定回复功能，支持屏蔽指定用户和群聊
        """
        try:
            if context and context.type == ContextType.TEXT:
                # 获取用户信息
                user_name = "未知用户"
                group_name = None

                try:
                    if hasattr(context, 'kwargs') and 'msg' in context.kwargs:
                        msg = context.kwargs['msg']
                        if hasattr(msg, 'from_user_nickname'):
                            user_name = msg.from_user_nickname or "未知用户"
                        if hasattr(msg, 'is_group') and msg.is_group:
                            if hasattr(msg, 'other_user_nickname'):
                                group_name = msg.other_user_nickname
                except Exception as e:
                    logger.debug(f"获取用户信息失败: {e}")

                # 显示接收消息信息
                if group_name:
                    message = f"📨 接收到新的消息 - 群聊：{group_name}，用户：{user_name}，内容：{query}"
                else:
                    message = f"📨 接收到新的消息 - 用户：{user_name}，内容：{query}"

                logger.info(message)
                print(f"[DEBUG] {message}")  # 添加调试输出
                stats_manager.log_message(message)  # 直接发送到GUI

                # 统计接收到的消息
                stats_manager.increment_received()

                # 安全检查是否需要屏蔽此消息
                try:
                    logger.info(f"[MockBot] 开始屏蔽检查...")
                    should_block = self._should_block_message(context)
                    logger.info(f"[MockBot] 屏蔽检查结果: {should_block}")
                    if should_block:
                        if group_name:
                            block_message = f"🚫 消息已被屏蔽 - 群聊：{group_name}，用户：{user_name}"
                        else:
                            block_message = f"🚫 消息已被屏蔽 - 用户：{user_name}"

                        logger.info(block_message)
                        stats_manager.log_message(block_message)  # 直接发送到GUI
                        return None  # 返回None表示不回复
                except Exception as e:
                    logger.warning(f"[MockBot] 屏蔽检查失败，继续正常回复: {e}")
                    import traceback
                    traceback.print_exc()
                    # 屏蔽检查失败时，继续正常回复流程

                # 每次都从配置中读取最新的回复文本
                try:
                    auto_reply_text = conf().get("auto_reply_text", "你好啊")
                except Exception as e:
                    logger.warning(f"[MockBot] 获取回复文本失败，使用默认文本: {e}")
                    auto_reply_text = "你好啊"

                reply = Reply(ReplyType.TEXT, auto_reply_text)

                # 显示回复消息信息
                if group_name:
                    reply_message = f"✅ 已自动回复 - 群聊：{group_name}，用户：{user_name}，回复：{auto_reply_text}"
                else:
                    reply_message = f"✅ 已自动回复 - 用户：{user_name}，回复：{auto_reply_text}"

                logger.info(reply_message)
                print(f"[DEBUG] {reply_message}")  # 添加调试输出
                stats_manager.log_message(reply_message)  # 直接发送到GUI

                # 统计成功回复的消息
                stats_manager.increment_replied()

                return reply
            else:
                reply = Reply(ReplyType.ERROR, "不支持的消息类型")
                return reply

        except Exception as e:
            logger.error(f"[MockBot] 回复失败: {e}")

            # 统计失败的消息
            stats_manager.increment_failed()

            # 确保即使出错也返回一个有效的回复，避免影响整个消息处理流程
            try:
                reply = Reply(ReplyType.TEXT, "系统暂时无法回复，请稍后再试")
                return reply
            except:
                return None

    def _should_block_message(self, context):
        """
        检查是否应该屏蔽此消息
        """
        try:
            # 安全获取屏蔽列表配置
            try:
                single_chat_block_list = conf().get("single_chat_block_list", [])
                group_chat_block_list = conf().get("group_chat_block_list", [])
            except Exception as e:
                logger.warning(f"[MockBot] 获取屏蔽配置失败: {e}，使用空列表")
                single_chat_block_list = []
                group_chat_block_list = []

            # 如果屏蔽列表为空，直接返回不屏蔽
            if not single_chat_block_list and not group_chat_block_list:
                logger.info(f"[MockBot] 屏蔽列表为空，不进行屏蔽")
                return False

            logger.info(f"[MockBot] 屏蔽检查 - 单聊屏蔽列表: {single_chat_block_list}")
            logger.info(f"[MockBot] 屏蔽检查 - 群聊屏蔽列表: {group_chat_block_list}")

            # 安全获取消息对象
            msg = None
            is_group = False

            try:
                if hasattr(context, 'kwargs') and isinstance(context.kwargs, dict):
                    if 'msg' in context.kwargs:
                        msg = context.kwargs['msg']
                    is_group = context.kwargs.get('isgroup', False)
            except Exception as e:
                logger.warning(f"[MockBot] 获取消息对象失败: {e}")
                return False

            if not msg:
                logger.info(f"[MockBot] 未找到消息对象，不进行屏蔽检查")
                return False

            try:
                # 检查是否是群聊消息
                msg_is_group = getattr(msg, 'is_group', is_group)
                logger.info(f"[MockBot] 消息类型检查 - is_group: {msg_is_group}")

                if msg_is_group:
                    # 群聊消息 - 检查群聊名称
                    group_name = None
                    try:
                        group_name = getattr(msg, 'other_user_nickname', None)
                        if not group_name:
                            # 尝试其他可能的属性
                            group_name = getattr(msg, 'from_user_nickname', None)
                    except Exception as e:
                        logger.info(f"[MockBot] 获取群聊名称失败: {e}")

                    logger.info(f"[MockBot] 群聊消息，群名: '{group_name}'")
                    if group_name and isinstance(group_name, str) and group_name.strip():
                        if group_name in group_chat_block_list:
                            logger.info(f"[MockBot] 群聊 '{group_name}' 在屏蔽列表中")
                            return True
                        else:
                            logger.info(f"[MockBot] 群聊 '{group_name}' 不在屏蔽列表中")

                else:
                    # 单聊消息 - 检查发送者昵称
                    user_name = None
                    try:
                        user_name = getattr(msg, 'from_user_nickname', None)
                    except Exception as e:
                        logger.info(f"[MockBot] 获取用户昵称失败: {e}")

                    logger.info(f"[MockBot] 单聊消息，用户: '{user_name}'")
                    if user_name and isinstance(user_name, str) and user_name.strip():
                        if user_name in single_chat_block_list:
                            logger.info(f"[MockBot] 单聊用户 '{user_name}' 在屏蔽列表中")
                            return True
                        else:
                            logger.info(f"[MockBot] 单聊用户 '{user_name}' 不在屏蔽列表中")
                    else:
                        logger.info(f"[MockBot] 用户名为空或无效: '{user_name}'")

            except Exception as e:
                logger.warning(f"[MockBot] 屏蔽检查过程中出错: {e}")
                return False

            logger.debug(f"[MockBot] 消息未被屏蔽")
            return False

        except Exception as e:
            logger.error(f"[MockBot] 屏蔽检查完全失败: {e}")
            # 不打印完整堆栈，避免日志过多
            return False  # 出错时不屏蔽，确保正常功能

    def reply_text(self, session, query, context=None):
        """
        文本消息回复
        """
        return self.reply(query, context)
