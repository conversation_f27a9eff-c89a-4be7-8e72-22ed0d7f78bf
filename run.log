[INFO][2025-08-20 04:18:56][config.py:313] - 配置文件不存在，将使用config-template.json模板
[DEBUG][2025-08-20 04:18:56][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 04:18:56][config.py:342] - [INIT] load config: {'dify_api_base': 'https://api.dify.ai/v1', 'dify_api_key': 'app*****xxx', 'dify_app_type': 'chatbot', 'channel_type': 'gewechat', 'gewechat_app_id': '', 'gewechat_token': '', 'gewechat_base_url': '', 'gewechat_callback_url': '', 'gewechat_download_url': '', 'debug': True, 'model': 'dify', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': True, 'speech_recognition': True, 'voice_reply_voice': True, 'voice_to_text': 'dify', 'text_to_voice': 'dify'}
[INFO][2025-08-20 04:18:56][config.py:268] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 04:18:56][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[INFO][2025-08-20 04:18:57][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 04:18:59][wework_channel.py:195] - 登录信息:>>>user_id:1688851272749679>>>>>>>>name:童树运
[INFO][2025-08-20 04:18:59][wework_channel.py:196] - 企业微信登录成功，开始获取联系人和群聊信息...
[WARNING][2025-08-20 04:18:59][wework_channel.py:173] - 获取数据失败，重试第1次······
[INFO][2025-08-20 04:19:04][wework_channel.py:229] - wework程序初始化完成········
