[INFO][2025-08-20 03:34:08][config.py:313] - 配置文件不存在，将使用config-template.json模板
[INFO][2025-08-20 03:35:14][config.py:313] - 配置文件不存在，将使用config-template.json模板
[INFO][2025-08-20 03:38:25][config.py:313] - 配置文件不存在，将使用config-template.json模板
[INFO][2025-08-20 03:40:25][config.py:313] - 配置文件不存在，将使用config-template.json模板
[DEBUG][2025-08-20 03:40:25][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 03:40:25][config.py:342] - [INIT] load config: {'dify_api_base': 'https://api.dify.ai/v1', 'dify_api_key': 'app*****xxx', 'dify_app_type': 'chatbot', 'channel_type': 'gewechat', 'gewechat_app_id': '', 'gewechat_token': '', 'gewechat_base_url': '', 'gewechat_callback_url': '', 'gewechat_download_url': '', 'debug': True, 'model': 'dify', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': True, 'speech_recognition': True, 'voice_reply_voice': True, 'voice_to_text': 'dify', 'text_to_voice': 'dify'}
[INFO][2025-08-20 03:40:25][config.py:268] - [Config] User datas file not found, ignore.
[INFO][2025-08-20 03:41:55][config.py:313] - 配置文件不存在，将使用config-template.json模板
[DEBUG][2025-08-20 03:41:55][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 03:41:55][config.py:342] - [INIT] load config: {'dify_api_base': 'https://api.dify.ai/v1', 'dify_api_key': 'app*****xxx', 'dify_app_type': 'chatbot', 'channel_type': 'gewechat', 'gewechat_app_id': '', 'gewechat_token': '', 'gewechat_base_url': '', 'gewechat_callback_url': '', 'gewechat_download_url': '', 'debug': True, 'model': 'dify', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': True, 'speech_recognition': True, 'voice_reply_voice': True, 'voice_to_text': 'dify', 'text_to_voice': 'dify'}
[INFO][2025-08-20 03:41:55][config.py:268] - [Config] User datas file not found, ignore.
[INFO][2025-08-20 03:52:09][config.py:313] - 配置文件不存在，将使用config-template.json模板
[DEBUG][2025-08-20 03:52:09][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 03:52:09][config.py:342] - [INIT] load config: {'dify_api_base': 'https://api.dify.ai/v1', 'dify_api_key': 'app*****xxx', 'dify_app_type': 'chatbot', 'channel_type': 'gewechat', 'gewechat_app_id': '', 'gewechat_token': '', 'gewechat_base_url': '', 'gewechat_callback_url': '', 'gewechat_download_url': '', 'debug': True, 'model': 'dify', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': True, 'speech_recognition': True, 'voice_reply_voice': True, 'voice_to_text': 'dify', 'text_to_voice': 'dify'}
[INFO][2025-08-20 03:52:09][config.py:268] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 03:52:10][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[INFO][2025-08-20 03:52:11][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 03:52:12][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 03:52:12][wework_channel.py:196] - 企业微信登录成功，开始获取联系人和群聊信息...
[WARNING][2025-08-20 03:52:12][wework_channel.py:173] - 获取数据失败，重试第1次······
[INFO][2025-08-20 03:52:18][wework_channel.py:229] - wework程序初始化完成········
[INFO][2025-08-20 03:53:20][config.py:313] - 配置文件不存在，将使用config-template.json模板
[DEBUG][2025-08-20 03:53:20][config.py:340] - [INIT] set log level to DEBUG
[INFO][2025-08-20 03:53:20][config.py:342] - [INIT] load config: {'dify_api_base': 'https://api.dify.ai/v1', 'dify_api_key': 'app*****xxx', 'dify_app_type': 'chatbot', 'channel_type': 'gewechat', 'gewechat_app_id': '', 'gewechat_token': '', 'gewechat_base_url': '', 'gewechat_callback_url': '', 'gewechat_download_url': '', 'debug': True, 'model': 'dify', 'single_chat_prefix': [''], 'single_chat_reply_prefix': '', 'group_chat_prefix': ['@bot'], 'group_name_white_list': ['ALL_GROUP'], 'image_recognition': True, 'speech_recognition': True, 'voice_reply_voice': True, 'voice_to_text': 'dify', 'text_to_voice': 'dify'}
[INFO][2025-08-20 03:53:20][config.py:268] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 03:53:21][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[INFO][2025-08-20 03:53:21][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 03:53:22][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 03:53:22][wework_channel.py:196] - 企业微信登录成功，开始获取联系人和群聊信息...
[WARNING][2025-08-20 03:53:22][wework_channel.py:173] - 获取数据失败，重试第1次······
[INFO][2025-08-20 03:53:27][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 03:53:42][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQxqyTxQYY/7qbqpmAgAMg6Qo=', 'at_list': [], 'content': '4', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38768', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776377'}, 'type': 11041}
[DEBUG][2025-08-20 03:53:42][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 03:53:42][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 03:53:42][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 03:53:42][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=4, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 03:53:44][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 03:53:44][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQxqyTxQYY/7qbqpmAgAMg6Qo=", "at_list": [], "content": "4", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38768", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776377"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=4, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 03:53:44][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 03:53:44][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=4, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA394D90>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 03:53:44][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=4, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA394D90>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 03:53:44][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=4
[INFO][2025-08-20 03:53:44][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 03:53:44][mock_bot.py:75] - 📨 接收到新的消息 - 用户：尚二松，内容：4
[INFO][2025-08-20 03:53:44][mock_bot.py:84] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 03:53:44][mock_bot.py:158] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 03:53:44][mock_bot.py:86] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 03:53:44][mock_bot.py:117] - ✅ 已自动回复 - 用户：尚二松，回复：我已经下班，有问题明天再说，急事请电联
[DEBUG][2025-08-20 03:53:44][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联)
[DEBUG][2025-08-20 03:53:44][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), context: Context(type=TEXT, content=4, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA394D90>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001F7DCACF730>})
[DEBUG][2025-08-20 03:53:44][wework_channel.py:276] - context: Context(type=TEXT, content=4, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA394D90>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001F7DCACF730>})
[DEBUG][2025-08-20 03:53:44][wework_channel.py:281] - match: None
[INFO][2025-08-20 03:53:44][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电联), receiver=S:****************_****************
[DEBUG][2025-08-20 03:53:44][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 03:53:45][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQyKyTxQYY76TDoYWAgAMgIg==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电联', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38769', 'receiver': '****************', 'send_time': '1755633224', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776380'}, 'type': 11041}
[DEBUG][2025-08-20 03:53:45][wework_channel.py:129] - 自己发的，直接结束
[DEBUG][2025-08-20 03:54:10][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ4qyTxQYY/7qbqpmAgAMg6wo=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '38770', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1776384'}, 'type': 11041}
[DEBUG][2025-08-20 03:54:10][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 03:54:10][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 1096, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 03:54:10][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 03:54:10][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 03:54:12][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 03:54:12][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQ4qyTxQYY/7qbqpmAgAMg6wo=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "38770", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1776384"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 03:54:12][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 03:54:13][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA396EF0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 03:54:13][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA396EF0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 03:54:13][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 03:54:13][mock_bot.py:75] - 📨 接收到新的消息 - 用户：尚二松，内容：1
[INFO][2025-08-20 03:54:13][mock_bot.py:84] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 03:54:13][mock_bot.py:158] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 03:54:13][mock_bot.py:86] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 03:54:13][mock_bot.py:117] - ✅ 已自动回复 - 用户：尚二松，回复：我已经下班，有问题明天再说，急事请电
[DEBUG][2025-08-20 03:54:13][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电)
[DEBUG][2025-08-20 03:54:13][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA396EF0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001F7DCACF730>})
[DEBUG][2025-08-20 03:54:13][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x000001F7EA396EF0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x000001F7DCACF730>})
[DEBUG][2025-08-20 03:54:13][wework_channel.py:281] - match: None
[INFO][2025-08-20 03:54:13][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=我已经下班，有问题明天再说，急事请电), receiver=S:****************_****************
[DEBUG][2025-08-20 03:54:13][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 03:54:13][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQ5ayTxQYY76TDoYWAgAMgIw==', 'at_list': [], 'content': '我已经下班，有问题明天再说，急事请电', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '38771', 'receiver': '****************', 'send_time': '1755633253', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1776387'}, 'type': 11041}
[DEBUG][2025-08-20 03:54:13][wework_channel.py:129] - 自己发的，直接结束
