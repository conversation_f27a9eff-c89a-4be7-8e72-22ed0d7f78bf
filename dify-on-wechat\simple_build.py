#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信自动回复系统简化打包脚本
避免复杂依赖分析问题
"""

import os
import sys
import subprocess
import shutil

def main():
    print("=" * 60)
    print("🎯 企业微信自动回复系统 - 简化打包工具")
    print("=" * 60)
    print()
    
    # 清理之前的构建
    print("🧹 清理之前的构建文件...")
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("✅ 删除build目录")
    if os.path.exists("dist"):
        shutil.rmtree("dist")
        print("✅ 删除dist目录")
    
    # 删除之前的spec文件
    spec_files = ["企业微信自动回复系统.spec", "gui_app.spec"]
    for spec in spec_files:
        if os.path.exists(spec):
            os.remove(spec)
            print(f"✅ 删除{spec}")
    
    print()
    print("📦 开始简化打包...")
    
    # 使用最基本的PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onedir",                    # 单目录模式
        "--windowed",                  # 窗口模式
        "--name=企业微信自动回复系统",    # 程序名称
        "--icon=2.ico",               # 图标
        "--add-data=2.ico;.",         # 添加图标
        "--add-data=12.jpg;.",        # 添加技术支持图片
        "--add-data=33.png;.",        # 添加请我喝茶图片
        "--add-data=config-gui.json;.", # 添加配置文件
        "--exclude-module=numpy",      # 排除大型库
        "--exclude-module=matplotlib",
        "--exclude-module=scipy",
        "--exclude-module=pandas",
        "--exclude-module=PIL",
        "--exclude-module=cv2",
        "--exclude-module=tensorflow",
        "--exclude-module=torch",
        "--exclude-module=tkinter",
        "--exclude-module=pygame",
        "--noconfirm",                # 不询问覆盖
        "gui_app.py"                  # 主文件
    ]
    
    try:
        print(f"执行命令: {' '.join(cmd)}")
        print("⏳ 正在打包，请耐心等待...")
        
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 基础打包成功！")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print("错误输出:")
        print(e.stderr)
        return False
    
    # 检查输出目录
    dist_dir = "dist/企业微信自动回复系统"
    if not os.path.exists(dist_dir):
        print("❌ 找不到输出目录")
        return False
    
    print()
    print("📁 复制额外文件...")
    
    # 复制必要的Python模块
    modules_to_copy = [
        "gui",
        "bot", 
        "channel",
        "bridge",
        "common"
    ]
    
    for module in modules_to_copy:
        if os.path.exists(module):
            dst_path = os.path.join(dist_dir, module)
            if os.path.exists(dst_path):
                shutil.rmtree(dst_path)
            shutil.copytree(module, dst_path)
            print(f"✅ 复制模块: {module}")
    
    # 复制其他必要文件
    other_files = [
        "config.py",
        "README.md",
        "GUI使用说明.md",
        "屏蔽功能说明.md"
    ]
    
    for file in other_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_dir)
            print(f"✅ 复制文件: {file}")
    
    # 复制ntwork wheel文件
    ntwork_wheel = "ntwork-0.1.3-cp310-cp310-win_amd64.whl"
    if os.path.exists(ntwork_wheel):
        shutil.copy2(ntwork_wheel, dist_dir)
        print(f"✅ 复制ntwork: {ntwork_wheel}")
    
    print()
    print("📝 创建启动脚本...")
    
    # 创建启动脚本
    bat_content = '''@echo off
chcp 65001 > nul
title 企业微信自动回复系统

echo.
echo ============================================================
echo 🚀 企业微信自动回复系统
echo ============================================================
echo.
echo 💡 使用提示：
echo    1. 确保企业微信客户端已启动并登录
echo    2. 首次使用请先配置自动回复内容  
echo    3. 如有问题请查看使用说明文档
echo.
echo ⏳ 正在启动程序，请稍候...
echo.

"企业微信自动回复系统.exe"

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，可能的原因：
    echo    1. 企业微信客户端未安装或未登录
    echo    2. 缺少必要的系统组件
    echo    3. 权限不足
    echo.
    echo 💡 解决方法：
    echo    1. 确保企业微信客户端正常运行
    echo    2. 尝试以管理员身份运行
    echo    3. 查看README.md获取更多帮助
    echo.
    pause
)
'''
    
    bat_path = os.path.join(dist_dir, "启动程序.bat")
    with open(bat_path, 'w', encoding='utf-8') as f:
        f.write(bat_content)
    print("✅ 创建启动脚本: 启动程序.bat")
    
    # 创建使用说明
    readme_content = '''# 企业微信自动回复系统

## 🚀 快速开始

1. 双击 `启动程序.bat` 或 `企业微信自动回复系统.exe` 启动程序
2. 在配置界面设置自动回复内容
3. 添加需要屏蔽的用户或群聊（可选）
4. 点击启动服务开始自动回复

## 📋 系统要求

- Windows 7/8/10/11
- 已安装企业微信客户端
- 企业微信已登录

## ⚠️ 注意事项

1. 首次运行可能需要管理员权限
2. 确保企业微信客户端正常运行
3. 建议先在测试环境中验证功能

## 📞 技术支持

程序内置技术支持功能，点击"技术支持"按钮获取帮助。

## 📁 文件说明

- `企业微信自动回复系统.exe` - 主程序
- `启动程序.bat` - 启动脚本  
- `config-gui.json` - 配置文件
- `gui/` - 界面模块
- `bot/` - 机器人模块
- `channel/` - 通道模块
- `bridge/` - 桥接模块
- `common/` - 公共模块
'''
    
    readme_path = os.path.join(dist_dir, "使用说明.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 创建使用说明: 使用说明.txt")
    
    # 计算大小
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(dist_dir):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            total_size += os.path.getsize(filepath)
    
    size_mb = total_size / (1024 * 1024)
    
    print()
    print("=" * 60)
    print("🎉 打包完成！")
    print("=" * 60)
    print(f"📁 输出目录: {dist_dir}")
    print(f"📦 打包大小: {size_mb:.1f} MB")
    print(f"🚀 主程序: 企业微信自动回复系统.exe")
    print(f"📝 启动脚本: 启动程序.bat")
    print()
    print("💡 部署说明:")
    print("   1. 将整个 '企业微信自动回复系统' 文件夹复制到目标电脑")
    print("   2. 确保目标电脑已安装企业微信客户端")
    print("   3. 双击 '启动程序.bat' 运行程序")
    print()
    print("📋 测试建议:")
    print("   □ 在当前电脑测试exe是否能正常启动")
    print("   □ 测试配置功能是否正常")
    print("   □ 测试帮助窗口是否能打开")
    print("   □ 测试图片是否正常显示")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    print()
    input("按回车键退出...")
    sys.exit(0 if success else 1)
