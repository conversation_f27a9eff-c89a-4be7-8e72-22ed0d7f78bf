@echo off
chcp 65001
echo 开始打包企业微信自动回复系统...

REM 清理之前的构建文件
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

REM 使用PyInstaller打包
pyinstaller --clean --noconfirm "企业微信自动回复系统.spec"

if %errorlevel% equ 0 (
    echo.
    echo ✅ 打包成功！
    echo 可执行文件位于: dist\企业微信自动回复系统\
    echo.
    echo 使用说明:
    echo 1. 将整个 dist\企业微信自动回复系统 文件夹复制到目标机器
    echo 2. 双击运行 企业微信自动回复系统.exe
    echo 3. 首次运行时请配置相关参数
) else (
    echo.
    echo ❌ 打包失败，请检查错误信息
)

pause
