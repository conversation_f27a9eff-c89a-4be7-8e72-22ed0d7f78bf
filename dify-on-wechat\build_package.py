#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信自动回复系统专业打包脚本
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

class PackageBuilder:
    def __init__(self):
        self.project_root = os.path.dirname(os.path.abspath(__file__))
        self.dist_dir = os.path.join(self.project_root, "dist", "企业微信自动回复系统")
        
    def print_header(self):
        """打印标题"""
        print("=" * 70)
        print("🎯 企业微信自动回复系统 - 专业打包工具")
        print("=" * 70)
        print()
        
    def check_environment(self):
        """检查环境"""
        print("🔍 检查打包环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print("❌ PyInstaller未安装，正在安装...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装完成")
            
        # 检查PyQt5
        try:
            import PyQt5
            print("✅ PyQt5已安装")
        except ImportError:
            print("❌ PyQt5未安装，请先安装: pip install PyQt5")
            return False
            
        # 检查必要文件
        required_files = ['gui_app.py', '2.ico', 'config-gui.json']
        for file in required_files:
            if os.path.exists(os.path.join(self.project_root, file)):
                print(f"✅ 找到文件: {file}")
            else:
                print(f"⚠️ 文件不存在: {file}")
                
        print()
        return True
        
    def clean_build(self):
        """清理构建文件"""
        print("🧹 清理之前的构建文件...")
        
        dirs_to_clean = ['build', 'dist']
        for dir_name in dirs_to_clean:
            dir_path = os.path.join(self.project_root, dir_name)
            if os.path.exists(dir_path):
                shutil.rmtree(dir_path)
                print(f"✅ 删除目录: {dir_name}")
                
        # 清理spec文件
        spec_files = ['企业微信自动回复系统.spec']
        for spec_file in spec_files:
            spec_path = os.path.join(self.project_root, spec_file)
            if os.path.exists(spec_path):
                os.remove(spec_path)
                print(f"✅ 删除文件: {spec_file}")
                
        print()
        
    def build_executable(self):
        """构建可执行文件"""
        print("🔨 开始构建可执行文件...")
        
        try:
            # 使用自定义spec文件
            spec_file = "package_build.spec"
            
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm",
                spec_file
            ]
            
            print(f"📦 执行命令: {' '.join(cmd)}")
            print("⏳ 正在构建，请耐心等待...")
            
            # 执行构建
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                text=True,
                cwd=self.project_root
            )
            
            # 实时显示输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    if "INFO:" in output:
                        print(f"📋 {output.strip()}")
                    elif "WARNING:" in output:
                        print(f"⚠️ {output.strip()}")
                    elif "ERROR:" in output:
                        print(f"❌ {output.strip()}")
                        
            rc = process.poll()
            if rc == 0:
                print("✅ 构建成功！")
                return True
            else:
                print("❌ 构建失败")
                return False
                
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
            
    def copy_additional_files(self):
        """复制额外文件"""
        print("📁 复制额外文件...")
        
        if not os.path.exists(self.dist_dir):
            print(f"❌ 找不到输出目录: {self.dist_dir}")
            return False
            
        # 复制文档文件
        doc_files = [
            "README.md",
            "GUI使用说明.md", 
            "屏蔽功能说明.md",
            "打包说明.md"
        ]
        
        for doc_file in doc_files:
            src_path = os.path.join(self.project_root, doc_file)
            if os.path.exists(src_path):
                dst_path = os.path.join(self.dist_dir, doc_file)
                shutil.copy2(src_path, dst_path)
                print(f"✅ 复制文档: {doc_file}")
                
        # 创建必要目录
        dirs_to_create = ['tmp', 'logs']
        for dir_name in dirs_to_create:
            dir_path = os.path.join(self.dist_dir, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
                print(f"✅ 创建目录: {dir_name}")
                
        print()
        return True
        
    def create_startup_scripts(self):
        """创建启动脚本"""
        print("📝 创建启动脚本...")
        
        # 创建批处理启动脚本
        bat_content = '''@echo off
chcp 65001 > nul
title 企业微信自动回复系统

echo.
echo ============================================================
echo 🚀 企业微信自动回复系统
echo ============================================================
echo.
echo 💡 使用提示：
echo    1. 确保企业微信客户端已启动并登录
echo    2. 首次使用请先配置自动回复内容
echo    3. 如有问题请查看使用说明文档
echo.
echo ⏳ 正在启动程序，请稍候...
echo.

"企业微信自动回复系统.exe"

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，请检查：
    echo    1. 是否已安装企业微信客户端
    echo    2. 是否有足够的系统权限
    echo    3. 查看错误日志获取详细信息
    echo.
    pause
) else (
    echo.
    echo ✅ 程序已正常退出
    echo.
)
'''
        
        bat_path = os.path.join(self.dist_dir, "启动程序.bat")
        with open(bat_path, 'w', encoding='utf-8') as f:
            f.write(bat_content)
        print("✅ 创建启动脚本: 启动程序.bat")
        
        # 创建说明文件
        readme_content = '''# 企业微信自动回复系统

## 🚀 快速开始

1. **启动程序**：双击 `启动程序.bat` 或 `企业微信自动回复系统.exe`
2. **配置回复内容**：在配置界面设置自动回复文本
3. **设置屏蔽**：添加不需要自动回复的用户或群聊
4. **启动服务**：点击启动服务开始自动回复

## 📋 文件说明

- `企业微信自动回复系统.exe` - 主程序
- `启动程序.bat` - 启动脚本
- `config-gui.json` - 配置文件
- `2.ico` - 程序图标
- `12.jpg` - 技术支持联系方式
- `33.png` - 请我喝茶二维码

## ⚠️ 注意事项

1. 需要先安装并登录企业微信客户端
2. 首次运行可能需要管理员权限
3. 如遇问题请查看相关说明文档

## 📞 技术支持

如有问题请通过程序内的"技术支持"功能联系我们。
'''
        
        readme_path = os.path.join(self.dist_dir, "使用说明.txt")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ 创建使用说明: 使用说明.txt")
        
        print()
        return True
        
    def get_package_info(self):
        """获取打包信息"""
        if not os.path.exists(self.dist_dir):
            return None
            
        # 计算大小
        total_size = 0
        file_count = 0
        
        for dirpath, dirnames, filenames in os.walk(self.dist_dir):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)
                file_count += 1
                
        size_mb = total_size / (1024 * 1024)
        
        return {
            'size_mb': size_mb,
            'file_count': file_count,
            'path': self.dist_dir
        }
        
    def show_results(self):
        """显示结果"""
        info = self.get_package_info()
        
        print("=" * 70)
        print("🎉 打包完成！")
        print("=" * 70)
        
        if info:
            print(f"📁 输出目录: {info['path']}")
            print(f"📦 打包大小: {info['size_mb']:.1f} MB")
            print(f"📄 文件数量: {info['file_count']} 个")
            print(f"🚀 主程序: 企业微信自动回复系统.exe")
            print(f"📝 启动脚本: 启动程序.bat")
        
        print()
        print("💡 部署说明:")
        print("   1. 将整个文件夹复制到目标电脑")
        print("   2. 确保目标电脑已安装企业微信客户端")
        print("   3. 双击启动脚本或exe文件运行程序")
        print()
        print("📋 测试清单:")
        print("   □ 程序能正常启动")
        print("   □ GUI界面显示正常")
        print("   □ 配置功能正常")
        print("   □ 帮助和捐赠窗口能打开")
        print("   □ 图片文件正常显示")
        print("=" * 70)
        
    def build(self):
        """执行完整的构建流程"""
        self.print_header()
        
        # 检查环境
        if not self.check_environment():
            return False
            
        # 清理构建文件
        self.clean_build()
        
        # 构建可执行文件
        if not self.build_executable():
            return False
            
        # 复制额外文件
        if not self.copy_additional_files():
            return False
            
        # 创建启动脚本
        if not self.create_startup_scripts():
            return False
            
        # 显示结果
        self.show_results()
        
        return True

def main():
    """主函数"""
    builder = PackageBuilder()
    success = builder.build()
    
    if success:
        print("🎉 打包成功完成！")
    else:
        print("❌ 打包失败，请检查错误信息")
        
    input("\n按回车键退出...")
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
