websockets-13.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
websockets-13.1.dist-info/LICENSE,sha256=D0RRSZisognTSC0QIEqK3yqkKW_xV6NqXAki8igGMtM,1538
websockets-13.1.dist-info/METADATA,sha256=ev-RPhy697qdmChP2bZMkbdxCpj_caxzSX22M2q8GcA,6954
websockets-13.1.dist-info/RECORD,,
websockets-13.1.dist-info/WHEEL,sha256=1rwUt2BQv3N5S0pE0N9WlEu1uiKd-mQhEp4H-4r377U,99
websockets-13.1.dist-info/top_level.txt,sha256=CMpdKklxKsvZgCgyltxUWOHibZXZ1uYIVpca9xsQ8Hk,11
websockets/__init__.py,sha256=KeS6FuKrebi9D2m2SOOm59TfAKIG3vBi4o75-c5WD9Q,6135
websockets/__main__.py,sha256=VDn_5jH681C9iv9Dh6nEXthV8aGr6pwS6IOn_kr0vdQ,4915
websockets/__pycache__/__init__.cpython-38.pyc,,
websockets/__pycache__/__main__.cpython-38.pyc,,
websockets/__pycache__/auth.cpython-38.pyc,,
websockets/__pycache__/client.cpython-38.pyc,,
websockets/__pycache__/connection.cpython-38.pyc,,
websockets/__pycache__/datastructures.cpython-38.pyc,,
websockets/__pycache__/exceptions.cpython-38.pyc,,
websockets/__pycache__/frames.cpython-38.pyc,,
websockets/__pycache__/headers.cpython-38.pyc,,
websockets/__pycache__/http.cpython-38.pyc,,
websockets/__pycache__/http11.cpython-38.pyc,,
websockets/__pycache__/imports.cpython-38.pyc,,
websockets/__pycache__/protocol.cpython-38.pyc,,
websockets/__pycache__/server.cpython-38.pyc,,
websockets/__pycache__/streams.cpython-38.pyc,,
websockets/__pycache__/typing.cpython-38.pyc,,
websockets/__pycache__/uri.cpython-38.pyc,,
websockets/__pycache__/utils.cpython-38.pyc,,
websockets/__pycache__/version.cpython-38.pyc,,
websockets/asyncio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/asyncio/__pycache__/__init__.cpython-38.pyc,,
websockets/asyncio/__pycache__/async_timeout.cpython-38.pyc,,
websockets/asyncio/__pycache__/client.cpython-38.pyc,,
websockets/asyncio/__pycache__/compatibility.cpython-38.pyc,,
websockets/asyncio/__pycache__/connection.cpython-38.pyc,,
websockets/asyncio/__pycache__/messages.cpython-38.pyc,,
websockets/asyncio/__pycache__/server.cpython-38.pyc,,
websockets/asyncio/async_timeout.py,sha256=jMKMQcVKWtn09n6lqefqdpSYoMRlP4Ek60RjJ6B6Eco,9253
websockets/asyncio/client.py,sha256=VN4_Ww6qoJMRHXFR8fv5lqNpPP5kftYPDZ_SP8pqDwM,22261
websockets/asyncio/compatibility.py,sha256=ld5lpbBgJ8YGAi3tc1hMiIxuLeQWDg6pKDYBVbSQMl4,816
websockets/asyncio/connection.py,sha256=_EeGiX4es5cWlmrOlx4yuGK_1SHlfQkpz44u4HOjE_o,45623
websockets/asyncio/messages.py,sha256=Lgk3yf6SbrEZDoQGKssq4OxSgz_WYQWKGV9quFi3-_s,10147
websockets/asyncio/server.py,sha256=_TpZ86Kl4x2uBCaSo8XDmCkcLlc_AlTSGrv5B4v-Dko,37521
websockets/auth.py,sha256=EhKQwqisAZ4JyYsatBo6vlqzL7qR_PR_S2XDMsgNTi8,268
websockets/client.py,sha256=m3to_IyHuooBnW_kxfcYLf85X8x683VuRKMKDkFMIJ8,13934
websockets/connection.py,sha256=uIRi5jeS3zVIWq8DcEDJPousrYNTItPe0ioKxBr2hOs,335
websockets/datastructures.py,sha256=mxPmYv_ZQHn0CE-_MEtxKsgfvpbMLGkR0JfSDZowluQ,5869
websockets/exceptions.py,sha256=VxfhOGSbg6ofukbTkQZlbUQ2_KrqcWu0Ocpp98Jjldw,10986
websockets/extensions/__init__.py,sha256=HdQaQhOVkCR5RJVRKXG5Xmb6cMpAhLaKgRikYRzO64E,102
websockets/extensions/__pycache__/__init__.cpython-38.pyc,,
websockets/extensions/__pycache__/base.cpython-38.pyc,,
websockets/extensions/__pycache__/permessage_deflate.cpython-38.pyc,,
websockets/extensions/base.py,sha256=QZD-XlIRY_1CsNzm_t9uhqu0nPxlQ9pSF27ETVqUlKI,3013
websockets/extensions/permessage_deflate.py,sha256=ryetJ58GTeeh1NY-iZph5UuQ6GU05XnEGD1QzoD2on0,25331
websockets/frames.py,sha256=eZubN-6dh3utmqt0iz9n-Oh08zXSr5bNGRALTkyY_6M,13194
websockets/headers.py,sha256=HB1SdgV_BdEtJGuEE94EbZ2FnCdvhIub_YLAdWeNbw8,16510
websockets/http.py,sha256=73Id1K0872AgvffW3F84K2aRvXwnOCEf_OKP76bZKl8,496
websockets/http11.py,sha256=dwL6V59961fOIZzn7y9jYDLx8wsW_xHYJFZwrk-SOas,13761
websockets/imports.py,sha256=2tuCYXtCiHZ8pgS7HqNFX6gvGl1b-CjLXoI-riZVop8,2867
websockets/legacy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/legacy/__pycache__/__init__.cpython-38.pyc,,
websockets/legacy/__pycache__/auth.cpython-38.pyc,,
websockets/legacy/__pycache__/client.cpython-38.pyc,,
websockets/legacy/__pycache__/exceptions.cpython-38.pyc,,
websockets/legacy/__pycache__/framing.cpython-38.pyc,,
websockets/legacy/__pycache__/handshake.cpython-38.pyc,,
websockets/legacy/__pycache__/http.cpython-38.pyc,,
websockets/legacy/__pycache__/protocol.cpython-38.pyc,,
websockets/legacy/__pycache__/server.cpython-38.pyc,,
websockets/legacy/auth.py,sha256=yTK6hSIt7JcoFFni61rHnedRK8N2mfUK8HnjxPDtNJc,6757
websockets/legacy/client.py,sha256=umghXapIplyQQQ7Hi1z033eHjpWxmBu0fWOBTxSS-bE,27413
websockets/legacy/exceptions.py,sha256=phkYoSVk03qCn8LyeGLEcCzbymJiJcpNZLPoA7QBmJg,2045
websockets/legacy/framing.py,sha256=LW0Ii8uIgSUusDbtJf-xdkDUyGKl6qGlQhYGDPWZxko,6595
websockets/legacy/handshake.py,sha256=CRRxkJ96TIgWuoMIL0iol4jp4X7llMA8buX4gfo0SwE,5443
websockets/legacy/http.py,sha256=OPu_UljFJH74nxk5V-jnOOkqzdXcpJ6M0LynIpuZ740,7262
websockets/legacy/protocol.py,sha256=dSDzlQkFQj5CWFTaeMuQN2gAmMwnyVd50OA-jmY-14Y,65326
websockets/legacy/server.py,sha256=hZesKYI4h5upd54YuHW_AgxQHvxJsKLr-PMSaTPvRmg,46518
websockets/protocol.py,sha256=s3Yy7irv14NbhorwhqzwLzrIe6Xk_JZywyVUPRHm4vs,26244
websockets/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/server.py,sha256=Bqx5-322SlwR6UugGXknX61QpHfk9Ff00pJVAzuWSs0,21862
websockets/speedups.c,sha256=j9p_o0aR8U1dt7VXAFTOZzYlGjiSlHVrTujpEzqP19s,5989
websockets/speedups.cp38-win_amd64.pyd,sha256=wbHfN8QiX6byDy2GmxQ_TocXHb_2eav7XXKjvOYaaf8,11776
websockets/speedups.pyi,sha256=tYIifoH4Qponj6HT8rhk2MQOVrfBFW8T5SYcaxefOdA,56
websockets/streams.py,sha256=hnLRo8a66IeI31K1jvMokddyThMcEpz3F4wA3k5EOcw,4189
websockets/sync/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/sync/__pycache__/__init__.cpython-38.pyc,,
websockets/sync/__pycache__/client.cpython-38.pyc,,
websockets/sync/__pycache__/connection.cpython-38.pyc,,
websockets/sync/__pycache__/messages.cpython-38.pyc,,
websockets/sync/__pycache__/server.cpython-38.pyc,,
websockets/sync/__pycache__/utils.cpython-38.pyc,,
websockets/sync/client.py,sha256=EW20EaQYLbwnMhp6qMLWdUF85UycGTC8a-a9PazG3yc,11708
websockets/sync/connection.py,sha256=KjaQA0VII2bqrrnmC8JtJcLwKrFUOa8qJHtrXP_HGIQ,31485
websockets/sync/messages.py,sha256=22hPZceHO0Gzb9uzeX8h8K2-tvCTIe0D1xwDxs89ZRQ,10092
websockets/sync/server.py,sha256=9g2W-TZydeuOQDHDzD84nlhGdcAY5rDQDzRSb_8a-Yg,26335
websockets/sync/utils.py,sha256=JgQVxO_NcQXZd8m6hDeXpSQ_uN6GYF8u9XLHZ7msv3k,1152
websockets/typing.py,sha256=TtditQjL7lGy0_LRL-qEmOaDjoZTfWBezP1FQ6eetCQ,2234
websockets/uri.py,sha256=C8P9QQq1iflwE9ONPCLxiSTk3tztth63yLC1TrG_gq4,3232
websockets/utils.py,sha256=z3lSFywpnE4aifiJwYVPTgcwMLNDAkYDbdZUqkSwhPo,1201
websockets/version.py,sha256=_cxUVcvfx-sNSC8iNJfBZxIBfocwpRxaGxvtNP8PKbI,3294
