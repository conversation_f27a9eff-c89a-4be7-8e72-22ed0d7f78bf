@echo off
chcp 65001
title 企业微信自动回复系统
echo ========================================
echo        企业微信自动回复系统
echo ========================================
echo.
echo 正在启动系统，请稍候...
echo.

REM 检查虚拟环境是否存在
if not exist "pack_env\Scripts\python.exe" (
    echo ❌ 错误: 虚拟环境不存在
    echo 请确保 pack_env 目录存在
    pause
    exit /b 1
)

REM 启动应用
pack_env\Scripts\python.exe gui_app.py

REM 如果程序异常退出，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序异常退出，错误代码: %errorlevel%
    echo 💡 可能的解决方案:
    echo 1. 检查配置文件是否正确
    echo 2. 确保所有依赖文件存在
    echo 3. 查看上方的错误信息
    echo.
    pause
)
