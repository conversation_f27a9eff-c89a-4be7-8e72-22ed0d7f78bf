#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信自动回复系统 - 手动部署包创建工具
由于PyInstaller无法正常工作，使用此脚本创建完整的部署包
"""

import os
import sys
import shutil
import zipfile
import urllib.request
from pathlib import Path

class DeploymentCreator:
    def __init__(self):
        self.project_root = os.path.dirname(os.path.abspath(__file__))
        self.deploy_dir = os.path.join(self.project_root, "企业微信自动回复系统")
        self.python_url = "https://www.python.org/ftp/python/3.10.0/python-3.10.0-embed-amd64.zip"
        
    def print_header(self):
        """打印标题"""
        print("=" * 70)
        print("🎯 企业微信自动回复系统 - 手动部署包创建工具")
        print("=" * 70)
        print()
        
    def create_directory_structure(self):
        """创建目录结构"""
        print("📁 创建目录结构...")
        
        # 清理旧的部署目录
        if os.path.exists(self.deploy_dir):
            shutil.rmtree(self.deploy_dir)
            
        # 创建新的目录结构
        directories = [
            self.deploy_dir,
            os.path.join(self.deploy_dir, "python"),
            os.path.join(self.deploy_dir, "app"),
            os.path.join(self.deploy_dir, "app", "gui"),
            os.path.join(self.deploy_dir, "app", "bot"),
            os.path.join(self.deploy_dir, "app", "channel"),
            os.path.join(self.deploy_dir, "app", "bridge"),
            os.path.join(self.deploy_dir, "app", "common"),
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 创建目录: {os.path.basename(directory)}")
            
        print()
        
    def copy_application_files(self):
        """复制应用程序文件"""
        print("📄 复制应用程序文件...")
        
        # 主要文件
        main_files = [
            "gui_app.py",
            "config.py",
            "2.ico",
            "12.jpg", 
            "33.png",
            "config-gui.json",
            "ntwork-0.1.3-cp310-cp310-win_amd64.whl"
        ]
        
        for file in main_files:
            src_path = os.path.join(self.project_root, file)
            if os.path.exists(src_path):
                dst_path = os.path.join(self.deploy_dir, "app", file)
                shutil.copy2(src_path, dst_path)
                print(f"✅ 复制文件: {file}")
            else:
                print(f"⚠️ 文件不存在: {file}")
                
        # 复制模块目录
        modules = ["gui", "bot", "channel", "bridge", "common"]
        for module in modules:
            src_path = os.path.join(self.project_root, module)
            if os.path.exists(src_path):
                dst_path = os.path.join(self.deploy_dir, "app", module)
                if os.path.exists(dst_path):
                    shutil.rmtree(dst_path)
                shutil.copytree(src_path, dst_path)
                print(f"✅ 复制模块: {module}")
            else:
                print(f"⚠️ 模块不存在: {module}")
                
        print()
        
    def create_startup_script(self):
        """创建启动脚本"""
        print("📝 创建启动脚本...")
        
        startup_script = '''@echo off
chcp 65001 > nul
title 企业微信自动回复系统

echo ============================================================
echo 🚀 企业微信自动回复系统
echo ============================================================
echo.
echo 💡 使用提示：
echo    1. 确保企业微信客户端已启动并登录
echo    2. 首次使用请先配置自动回复内容
echo    3. 如有问题请查看使用说明文档
echo.
echo ⏳ 正在启动程序，请稍候...
echo.

cd /d "%~dp0"

REM 检查Python环境
if not exist "python\\python.exe" (
    echo ❌ Python环境未找到！
    echo 请确保python目录存在且包含python.exe
    echo.
    pause
    exit /b 1
)

REM 检查应用程序
if not exist "app\\gui_app.py" (
    echo ❌ 应用程序未找到！
    echo 请确保app目录存在且包含gui_app.py
    echo.
    pause
    exit /b 1
)

REM 启动程序
python\\python.exe app\\gui_app.py

if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败，可能的原因：
    echo    1. Python环境配置问题
    echo    2. 缺少必要依赖
    echo    3. 企业微信客户端未运行
    echo.
    echo 💡 解决方法：
    echo    1. 运行"安装依赖.bat"重新安装依赖
    echo    2. 确保企业微信客户端正常运行
    echo    3. 查看使用说明获取更多帮助
    echo.
    pause
)
'''
        
        script_path = os.path.join(self.deploy_dir, "启动程序.bat")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(startup_script)
        print("✅ 创建启动脚本: 启动程序.bat")
        
    def create_install_script(self):
        """创建依赖安装脚本"""
        print("📝 创建依赖安装脚本...")
        
        install_script = '''@echo off
chcp 65001 > nul
title 安装依赖

echo ============================================================
echo 🔧 企业微信自动回复系统 - 依赖安装
echo ============================================================
echo.

cd /d "%~dp0"

REM 检查Python环境
if not exist "python\\python.exe" (
    echo ❌ Python环境未找到！
    echo 请先下载并解压Python嵌入式版本到python目录
    echo.
    pause
    exit /b 1
)

echo 📦 正在安装程序依赖，请稍候...
echo.

echo 🔧 配置pip...
python\\python.exe -m ensurepip --default-pip
if errorlevel 1 (
    echo ⚠️ pip配置可能有问题，继续尝试安装依赖...
)

echo.
echo 📦 安装PyQt5...
python\\python.exe -m pip install PyQt5
if errorlevel 1 (
    echo ❌ PyQt5安装失败
    pause
    exit /b 1
)

echo 📦 安装requests...
python\\python.exe -m pip install requests
if errorlevel 1 (
    echo ❌ requests安装失败
    pause
    exit /b 1
)

echo 📦 安装chardet...
python\\python.exe -m pip install chardet
if errorlevel 1 (
    echo ❌ chardet安装失败
    pause
    exit /b 1
)

echo 📦 安装ntwork...
if exist "app\\ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    python\\python.exe -m pip install app\\ntwork-0.1.3-cp310-cp310-win_amd64.whl
    if errorlevel 1 (
        echo ⚠️ ntwork安装失败，但程序可能仍可正常运行
    )
) else (
    echo ⚠️ ntwork wheel文件未找到，跳过安装
)

echo.
echo ✅ 依赖安装完成！
echo 现在可以运行"启动程序.bat"启动应用程序
echo.
pause
'''
        
        script_path = os.path.join(self.deploy_dir, "安装依赖.bat")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(install_script)
        print("✅ 创建依赖安装脚本: 安装依赖.bat")
        
    def create_readme(self):
        """创建使用说明"""
        print("📝 创建使用说明...")
        
        readme_content = '''# 企业微信自动回复系统

## 🚀 快速开始

### 第一次使用：
1. 下载Python 3.10嵌入式版本并解压到python目录
2. 双击"安装依赖.bat"安装必要依赖
3. 双击"启动程序.bat"启动程序

### 日常使用：
1. 确保企业微信客户端已启动并登录
2. 双击"启动程序.bat"启动程序
3. 在程序中配置自动回复内容
4. 点击启动服务开始使用

## 📋 系统要求

- Windows 7/8/10/11 (64位)
- 已安装企业微信客户端
- 企业微信已登录
- 网络连接（用于下载依赖）

## 📁 目录结构

```
企业微信自动回复系统/
├── python/                    # Python运行环境（需要手动下载）
├── app/                       # 应用程序源码
├── 启动程序.bat               # 启动脚本
├── 安装依赖.bat               # 依赖安装脚本
└── 使用说明.txt               # 本文件
```

## 🔧 Python环境配置

### 下载Python嵌入式版本：
1. 访问：https://www.python.org/downloads/windows/
2. 找到Python 3.10.x
3. 下载"Windows embeddable package (64-bit)"
4. 解压到本目录下的python文件夹

### 或者使用直接链接：
https://www.python.org/ftp/python/3.10.0/python-3.10.0-embed-amd64.zip

## ⚠️ 注意事项

1. **首次运行**：必须先安装依赖
2. **网络要求**：安装依赖时需要网络连接
3. **企业微信**：确保企业微信客户端正常运行
4. **权限问题**：如遇权限问题，尝试以管理员身份运行

## 🐛 故障排除

### 程序无法启动：
1. 检查python目录是否存在且包含python.exe
2. 运行"安装依赖.bat"重新安装依赖
3. 确保企业微信客户端正常运行

### 依赖安装失败：
1. 检查网络连接
2. 尝试以管理员身份运行
3. 手动安装：python\\python.exe -m pip install PyQt5

### 企业微信连接失败：
1. 确保企业微信客户端已启动并登录
2. 检查企业微信版本是否兼容
3. 查看程序日志获取详细错误信息

## 📞 技术支持

如有问题请通过程序内的技术支持功能联系我们，或查看项目文档获取更多帮助。

## 📝 更新日志

- v1.0.0: 初始版本，支持企业微信自动回复功能
'''
        
        readme_path = os.path.join(self.deploy_dir, "使用说明.txt")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ 创建使用说明: 使用说明.txt")
        
    def create_python_config(self):
        """创建Python配置文件"""
        print("📝 创建Python配置...")
        
        # 创建python310._pth文件
        pth_content = '''python310.zip
.
app
Lib\\site-packages
'''
        
        pth_path = os.path.join(self.deploy_dir, "python", "python310._pth")
        with open(pth_path, 'w', encoding='utf-8') as f:
            f.write(pth_content)
        print("✅ 创建Python路径配置: python310._pth")
        
    def show_results(self):
        """显示结果"""
        print()
        print("=" * 70)
        print("🎉 部署包创建完成！")
        print("=" * 70)
        print(f"📁 部署目录: {self.deploy_dir}")
        print()
        print("📋 接下来的步骤：")
        print("1. 下载Python 3.10嵌入式版本")
        print("   链接: https://www.python.org/ftp/python/3.10.0/python-3.10.0-embed-amd64.zip")
        print("2. 解压到 python/ 目录")
        print("3. 运行 '安装依赖.bat' 安装依赖")
        print("4. 运行 '启动程序.bat' 启动程序")
        print()
        print("💡 提示：")
        print("- 整个文件夹可以复制到任何Windows电脑使用")
        print("- 目标电脑需要安装企业微信客户端")
        print("- 首次使用需要网络连接安装依赖")
        print("=" * 70)
        
    def create_deployment(self):
        """创建完整的部署包"""
        self.print_header()
        self.create_directory_structure()
        self.copy_application_files()
        self.create_startup_script()
        self.create_install_script()
        self.create_readme()
        self.create_python_config()
        self.show_results()
        
        return True

def main():
    """主函数"""
    creator = DeploymentCreator()
    success = creator.create_deployment()
    
    if success:
        print("\n🎉 部署包创建成功！")
    else:
        print("\n❌ 部署包创建失败")
        
    input("\n按回车键退出...")
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
