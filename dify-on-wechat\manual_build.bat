@echo off
chcp 65001 > nul
title 企业微信自动回复系统 - 手动打包工具

echo ============================================================
echo 🎯 企业微信自动回复系统 - 手动打包工具
echo ============================================================
echo.
echo 由于依赖复杂，使用手动打包方案
echo.

echo 🧹 清理之前的构建文件...
if exist "build" (
    rmdir /s /q "build"
    echo ✅ 删除build目录
)
if exist "dist" (
    rmdir /s /q "dist"
    echo ✅ 删除dist目录
)

echo.
echo 📦 执行基础打包...
pyinstaller --onedir --windowed --name="企业微信自动回复系统" gui_app.py

if errorlevel 1 (
    echo ❌ 基础打包失败
    pause
    exit /b 1
)

echo ✅ 基础打包完成
echo.

set "DIST_DIR=dist\企业微信自动回复系统"

echo 📁 复制Python模块...
if exist "gui" (
    xcopy /E /I /Y "gui" "%DIST_DIR%\gui"
    echo ✅ 复制gui模块
)

if exist "bot" (
    xcopy /E /I /Y "bot" "%DIST_DIR%\bot"
    echo ✅ 复制bot模块
)

if exist "channel" (
    xcopy /E /I /Y "channel" "%DIST_DIR%\channel"
    echo ✅ 复制channel模块
)

if exist "bridge" (
    xcopy /E /I /Y "bridge" "%DIST_DIR%\bridge"
    echo ✅ 复制bridge模块
)

if exist "common" (
    xcopy /E /I /Y "common" "%DIST_DIR%\common"
    echo ✅ 复制common模块
)

echo.
echo 📄 复制资源文件...
if exist "2.ico" (
    copy /Y "2.ico" "%DIST_DIR%\"
    echo ✅ 复制程序图标: 2.ico
)

if exist "12.jpg" (
    copy /Y "12.jpg" "%DIST_DIR%\"
    echo ✅ 复制技术支持图片: 12.jpg
)

if exist "33.png" (
    copy /Y "33.png" "%DIST_DIR%\"
    echo ✅ 复制请我喝茶图片: 33.png
)

if exist "config-gui.json" (
    copy /Y "config-gui.json" "%DIST_DIR%\"
    echo ✅ 复制配置文件: config-gui.json
)

if exist "config.py" (
    copy /Y "config.py" "%DIST_DIR%\"
    echo ✅ 复制配置模块: config.py
)

if exist "ntwork-0.1.3-cp310-cp310-win_amd64.whl" (
    copy /Y "ntwork-0.1.3-cp310-cp310-win_amd64.whl" "%DIST_DIR%\"
    echo ✅ 复制企业微信库: ntwork wheel文件
)

echo.
echo 📝 创建启动脚本...
(
echo @echo off
echo chcp 65001 ^> nul
echo title 企业微信自动回复系统
echo.
echo ============================================================
echo echo 🚀 企业微信自动回复系统
echo echo ============================================================
echo echo.
echo echo 💡 使用提示：
echo echo    1. 确保企业微信客户端已启动并登录
echo echo    2. 首次使用请先配置自动回复内容
echo echo    3. 如有问题请查看使用说明文档
echo echo.
echo echo ⏳ 正在启动程序，请稍候...
echo echo.
echo.
echo "企业微信自动回复系统.exe"
echo.
echo if errorlevel 1 ^(
echo     echo.
echo     echo ❌ 程序启动失败，可能的原因：
echo     echo    1. 企业微信客户端未安装或未登录
echo     echo    2. 缺少必要的系统组件
echo     echo    3. 权限不足
echo     echo.
echo     echo 💡 解决方法：
echo     echo    1. 确保企业微信客户端正常运行
echo     echo    2. 尝试以管理员身份运行
echo     echo    3. 查看使用说明获取更多帮助
echo     echo.
echo     pause
echo ^)
) > "%DIST_DIR%\启动程序.bat"

echo ✅ 创建启动脚本: 启动程序.bat

echo.
echo 📖 创建使用说明...
(
echo # 企业微信自动回复系统
echo.
echo ## 🚀 快速开始
echo.
echo 1. 双击 `启动程序.bat` 或 `企业微信自动回复系统.exe` 启动程序
echo 2. 在配置界面设置自动回复内容
echo 3. 添加需要屏蔽的用户或群聊（可选）
echo 4. 点击启动服务开始自动回复
echo.
echo ## 📋 系统要求
echo.
echo - Windows 7/8/10/11
echo - 已安装企业微信客户端
echo - 企业微信已登录
echo.
echo ## ⚠️ 注意事项
echo.
echo 1. 首次运行可能需要管理员权限
echo 2. 确保企业微信客户端正常运行
echo 3. 建议先在测试环境中验证功能
echo.
echo ## 📞 技术支持
echo.
echo 程序内置技术支持功能，点击"技术支持"按钮获取帮助。
echo.
echo ## 📁 文件说明
echo.
echo - `企业微信自动回复系统.exe` - 主程序
echo - `启动程序.bat` - 启动脚本
echo - `config-gui.json` - 配置文件
echo - `gui/` - 界面模块
echo - `bot/` - 机器人模块
echo - `channel/` - 通道模块
echo - `bridge/` - 桥接模块
echo - `common/` - 公共模块
) > "%DIST_DIR%\使用说明.txt"

echo ✅ 创建使用说明: 使用说明.txt

echo.
echo 📊 计算打包大小...
for /f "tokens=3" %%a in ('dir "%DIST_DIR%" /s /-c ^| find "个文件"') do set file_count=%%a
for /f "tokens=3" %%a in ('dir "%DIST_DIR%" /s /-c ^| find "字节"') do set total_bytes=%%a

echo.
echo ============================================================
echo 🎉 手动打包完成！
echo ============================================================
echo 📁 输出目录: %DIST_DIR%
echo 🚀 主程序: 企业微信自动回复系统.exe
echo 📝 启动脚本: 启动程序.bat
echo 📖 使用说明: 使用说明.txt
echo.
echo 💡 部署说明:
echo    1. 将整个 "企业微信自动回复系统" 文件夹复制到目标电脑
echo    2. 确保目标电脑已安装企业微信客户端
echo    3. 双击 "启动程序.bat" 运行程序
echo.
echo 📋 测试建议:
echo    □ 在当前电脑测试exe是否能正常启动
echo    □ 测试配置功能是否正常
echo    □ 测试帮助窗口是否能打开
echo    □ 测试图片是否正常显示
echo    □ 在没有Python环境的电脑上测试
echo.
echo ⚠️ 如果遇到问题:
echo    1. 查看 "完整打包方案.md" 获取详细说明
echo    2. 尝试以管理员身份运行
echo    3. 检查是否缺少必要文件
echo ============================================================

pause
