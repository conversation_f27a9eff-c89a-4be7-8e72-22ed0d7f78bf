# 企业微信自动回复系统

## 🚀 快速开始

### 第一次使用：
1. 下载Python 3.10嵌入式版本并解压到python目录
2. 双击"安装依赖.bat"安装必要依赖
3. 双击"启动程序.bat"启动程序

### 日常使用：
1. 确保企业微信客户端已启动并登录
2. 双击"启动程序.bat"启动程序
3. 在程序中配置自动回复内容
4. 点击启动服务开始使用

## 📋 系统要求

- Windows 7/8/10/11 (64位)
- 已安装企业微信客户端
- 企业微信已登录
- 网络连接（用于下载依赖）

## 📁 目录结构

```
企业微信自动回复系统/
├── python/                    # Python运行环境（需要手动下载）
├── app/                       # 应用程序源码
├── 启动程序.bat               # 启动脚本
├── 安装依赖.bat               # 依赖安装脚本
└── 使用说明.txt               # 本文件
```

## 🔧 Python环境配置

### 下载Python嵌入式版本：
1. 访问：https://www.python.org/downloads/windows/
2. 找到Python 3.10.x
3. 下载"Windows embeddable package (64-bit)"
4. 解压到本目录下的python文件夹

### 或者使用直接链接：
https://www.python.org/ftp/python/3.10.0/python-3.10.0-embed-amd64.zip

## ⚠️ 注意事项

1. **首次运行**：必须先安装依赖
2. **网络要求**：安装依赖时需要网络连接
3. **企业微信**：确保企业微信客户端正常运行
4. **权限问题**：如遇权限问题，尝试以管理员身份运行

## 🐛 故障排除

### 程序无法启动：
1. 检查python目录是否存在且包含python.exe
2. 运行"安装依赖.bat"重新安装依赖
3. 确保企业微信客户端正常运行

### 依赖安装失败：
1. 检查网络连接
2. 尝试以管理员身份运行
3. 手动安装：python\python.exe -m pip install PyQt5

### 企业微信连接失败：
1. 确保企业微信客户端已启动并登录
2. 检查企业微信版本是否兼容
3. 查看程序日志获取详细错误信息

## 📞 技术支持

如有问题请通过程序内的技术支持功能联系我们，或查看项目文档获取更多帮助。

## 📝 更新日志

- v1.0.0: 初始版本，支持企业微信自动回复功能
