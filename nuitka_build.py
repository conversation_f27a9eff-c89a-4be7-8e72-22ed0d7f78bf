#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Nuitka打包企业微信自动回复系统
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path

def clean_build():
    """清理构建目录"""
    dirs_to_clean = ['gui_app.build', 'gui_app.dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理可能的exe文件
    exe_files = ['gui_app.exe', '企业微信自动回复系统.exe']
    for exe_file in exe_files:
        if os.path.exists(exe_file):
            print(f"清理文件: {exe_file}")
            os.remove(exe_file)

def nuitka_build():
    """使用Nuitka进行打包"""
    print("开始使用Nuitka打包...")
    
    # 清理之前的构建
    clean_build()
    
    try:
        # Nuitka编译命令
        cmd = [
            'python', '-m', 'nuitka',
            '--onefile',  # 生成单个exe文件
            '--windows-disable-console',  # 隐藏控制台窗口
            '--enable-plugin=pyqt5',  # 启用PyQt5插件支持
            '--include-data-dir=gui=gui',  # 包含gui目录
            '--include-data-dir=common=common',  # 包含common目录
            '--include-data-dir=bridge=bridge',  # 包含bridge目录
            '--include-data-dir=channel=channel',  # 包含channel目录
            '--include-data-dir=bot=bot',  # 包含bot目录
            '--include-data-dir=translate=translate',  # 包含translate目录
            '--include-data-dir=voice=voice',  # 包含voice目录
            '--include-data-dir=plugins=plugins',  # 包含plugins目录
            '--include-data-file=config-template.json=config-template.json',  # 包含配置文件
            '--include-data-file=2.ico=2.ico',  # 包含图标文件
            '--include-data-file=12.jpg=12.jpg',  # 包含图片文件
            '--include-data-file=33.png=33.png',  # 包含图片文件
            '--windows-icon-from-ico=2.ico',  # 设置exe图标
            '--output-filename=企业微信自动回复系统.exe',  # 设置输出文件名
            'gui_app.py'  # 主程序文件
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        
        print("✅ Nuitka编译成功!")
        print(result.stdout)
        
        # 检查生成的exe文件
        exe_file = '企业微信自动回复系统.exe'
        if os.path.exists(exe_file):
            size_mb = os.path.getsize(exe_file) / (1024 * 1024)
            print(f"可执行文件: {exe_file}")
            print(f"文件大小: {size_mb:.1f} MB")
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka编译失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        return False
    except Exception as e:
        print(f"❌ 编译过程出错: {e}")
        return False

def nuitka_simple_build():
    """使用简化的Nuitka命令"""
    print("尝试简化的Nuitka编译...")
    
    try:
        # 简化的Nuitka命令
        cmd = [
            'python', '-m', 'nuitka',
            '--onefile',  # 生成单个exe文件
            '--windows-disable-console',  # 隐藏控制台窗口
            '--enable-plugin=pyqt5',  # 启用PyQt5插件支持
            '--output-filename=企业微信自动回复系统.exe',  # 设置输出文件名
            'gui_app.py'  # 主程序文件
        ]
        
        print(f"执行简化命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        
        print("✅ 简化Nuitka编译成功!")
        
        # 检查生成的exe文件
        exe_file = '企业微信自动回复系统.exe'
        if os.path.exists(exe_file):
            size_mb = os.path.getsize(exe_file) / (1024 * 1024)
            print(f"可执行文件: {exe_file}")
            print(f"文件大小: {size_mb:.1f} MB")
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 简化Nuitka编译失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 简化编译过程出错: {e}")
        return False

def main():
    """主函数"""
    print("企业微信自动回复系统 - Nuitka打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('gui_app.py'):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False
    
    # 检查Nuitka
    try:
        result = subprocess.run(['python', '-m', 'nuitka', '--version'], 
                              check=True, capture_output=True, text=True)
        print(f"Nuitka版本: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 错误: 未安装Nuitka，请运行: pip install nuitka")
        return False
    
    # 尝试完整编译
    print("\n🔄 尝试完整编译...")
    success = nuitka_build()
    
    if not success:
        print("\n🔄 完整编译失败，尝试简化编译...")
        success = nuitka_simple_build()
    
    if success:
        print("\n🎉 打包完成!")
        print("可执行文件: 企业微信自动回复系统.exe")
        print("\n使用说明:")
        print("1. 双击运行 企业微信自动回复系统.exe")
        print("2. 确保配置文件在同一目录")
        print("3. 首次运行时请配置相关参数")
        print("\n注意: 首次运行可能需要较长时间，请耐心等待")
    else:
        print("\n❌ 打包失败")
        print("建议:")
        print("1. 检查Python环境是否正常")
        print("2. 尝试在虚拟环境中重新安装依赖")
        print("3. 或者直接使用Python源码运行: python gui_app.py")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
