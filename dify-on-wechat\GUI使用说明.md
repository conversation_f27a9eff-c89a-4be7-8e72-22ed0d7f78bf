# 企业微信自动回复GUI系统 - 使用说明

## 系统简介

企业微信自动回复GUI系统是一个基于PyQt5的图形界面应用程序，用于管理企业微信的自动回复功能。系统支持单聊和群聊的自动回复，提供简洁易用的图形界面和系统托盘功能。

## 功能特性

### 核心功能
- ✅ 企业微信单聊自动回复
- ✅ 企业微信群聊自动回复
- ✅ 图形化配置管理
- ✅ 实时日志显示
- ✅ 系统托盘支持
- ✅ 服务启停控制
- ✅ 单聊/群聊屏蔽功能

### 界面特性
- 现代化GUI界面设计
- 实时日志显示（黑色背景，绿色文字）
- 直观的服务状态指示
- 简化的配置管理
- 系统托盘最小化支持

## 安装要求

### 系统要求
- Windows 7/8/10/11
- Python 3.7+
- 企业微信客户端

### 依赖库
```bash
pip install -r requirements.txt
```

主要依赖：
- PyQt5 >= 5.15.0
- ntwork (企业微信SDK)
- requests >= 2.28.2

## 使用方法

### 1. 启动应用
```bash
cd 企业微信/dify-on-wechat
python gui_app.py
```

### 2. 界面说明

#### 主界面布局
```
┌─────────────────────────────────────────┐
│ 企业微信自动回复系统                      │
├─────────────────────────────────────────┤
│ [启动服务] [停止服务] [已停止]            │
├─────────────────────────────────────────┤
│ 运行日志:                               │
│ ┌─────────────────────────────────────┐ │
│ │ 系统启动，等待用户操作...             │ │
│ │                                     │ │
│ └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│ 配置设置:                               │
│ 回复内容: [我已经下班，有问题明天再说]    │
│                              [保存配置] │
├─────────────────────────────────────────┤
│ 企业微信自动回复系统 v1.0                │
└─────────────────────────────────────────┘
```

#### 控制按钮
- **启动服务**: 启动企业微信自动回复服务
- **停止服务**: 停止正在运行的服务
- **状态显示**: 显示当前服务状态（已停止/运行中）

#### 日志区域
- 实时显示系统运行日志
- 黑色背景，绿色文字，模拟终端风格
- 自动滚动到最新日志

#### 配置区域
配置区域现在包含两个标签页：

**基本配置标签页**:
- **回复内容**: 设置自动回复的文本内容
- **保存配置**: 保存基本配置到文件

**屏蔽设置标签页**:
- **单聊屏蔽列表**: 管理不进行自动回复的用户昵称
- **群聊屏蔽列表**: 管理不进行自动回复的群聊名称
- **添加/删除**: 可以添加或删除屏蔽项
- **保存屏蔽配置**: 保存屏蔽设置

### 3. 系统托盘功能

#### 托盘图标
- 应用启动后会在系统托盘显示图标
- 图标显示当前服务状态
- 双击图标可显示/隐藏主窗口

#### 右键菜单
- **显示窗口**: 显示主窗口
- **隐藏窗口**: 隐藏主窗口到托盘
- **启动服务**: 快速启动企业微信服务
- **停止服务**: 快速停止服务
- **退出**: 完全退出应用程序

### 4. 使用流程

#### 首次使用
1. 启动GUI应用程序
2. 在"回复内容"中设置您的自动回复文本
3. 点击"保存配置"保存设置
4. 点击"启动服务"开始自动回复服务
5. 等待企业微信客户端启动和登录

#### 日常使用
1. 应用启动后会自动加载之前的配置
2. 可以随时修改回复内容并保存
3. 可以在"屏蔽设置"中管理不需要自动回复的用户和群聊
4. 通过按钮或托盘菜单控制服务启停
5. 查看日志了解服务运行状态

#### 屏蔽功能使用
1. **添加单聊屏蔽**:
   - 切换到"屏蔽设置"标签页
   - 在"单聊屏蔽列表"区域输入用户昵称
   - 点击"添加"按钮
   - 点击"保存屏蔽配置"

2. **添加群聊屏蔽**:
   - 在"群聊屏蔽列表"区域输入群聊名称
   - 点击"添加"按钮
   - 点击"保存屏蔽配置"

3. **删除屏蔽项**:
   - 在列表中选中要删除的项目
   - 点击"删除选中"按钮
   - 点击"保存屏蔽配置"

## 配置说明

### 配置文件
- **config-gui.json**: GUI版本的配置文件
- 包含自动回复内容、触发前缀等设置

### 主要配置项
```json
{
  "channel_type": "wework",
  "model": "mock",
  "auto_reply_text": "我已经下班，有问题明天再说，急事请电联",
  "single_chat_prefix": [""],
  "group_chat_prefix": ["@bot"],
  "group_name_white_list": ["ALL_GROUP"]
}
```

- **auto_reply_text**: 自动回复的文本内容
- **single_chat_prefix**: 单聊触发前缀（空字符串表示所有消息）
- **group_chat_prefix**: 群聊触发前缀（@bot表示需要@机器人）
- **group_name_white_list**: 群聊白名单（ALL_GROUP表示所有群）
- **single_chat_block_list**: 单聊屏蔽列表（不进行自动回复的用户昵称）
- **group_chat_block_list**: 群聊屏蔽列表（不进行自动回复的群聊名称）

## 常见问题

### Q: 启动服务失败怎么办？
A: 请检查：
1. 是否已安装ntwork库
2. 企业微信客户端是否正常
3. 查看日志中的具体错误信息

### Q: 自动回复不工作？
A: 请确认：
1. 服务已成功启动
2. 企业微信已正常登录
3. 回复内容已正确设置并保存

### Q: 如何退出程序？
A: 有三种方式：
1. 点击主窗口的关闭按钮（会最小化到托盘）
2. 右键托盘图标选择"退出"
3. 在主窗口按Alt+F4

### Q: 托盘图标不显示？
A: 请检查：
1. 系统是否支持托盘功能
2. 托盘区域是否被隐藏
3. 重启应用程序

## 技术支持

### 日志文件
- 运行日志仅在GUI中显示，不保存到文件
- 如需保存日志，可以从日志区域复制内容

### 故障排除
1. 查看GUI日志区域的错误信息
2. 确认所有依赖库已正确安装
3. 检查企业微信客户端状态
4. 重启应用程序

### 版本信息
- 版本: v1.0
- 基于: dify-on-wechat项目
- GUI框架: PyQt5
- 企业微信SDK: ntwork

## 注意事项

1. **企业微信登录**: 首次启动服务时需要手动登录企业微信
2. **网络连接**: 确保网络连接正常，企业微信需要联网使用
3. **权限要求**: 某些系统可能需要管理员权限运行
4. **防火墙**: 确保防火墙允许企业微信和Python程序的网络访问
5. **多开限制**: 同时只能运行一个实例

---

**企业微信自动回复GUI系统 v1.0**  
*让企业微信自动回复更简单*
