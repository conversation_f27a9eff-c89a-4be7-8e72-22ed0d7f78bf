(['E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui_app.py'],
 ['E:\\Desktop\\123\\企业微信\\dify-on-wechat',
  'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui',
  'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\bot',
  'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\channel',
  'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\bridge',
  'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\common'],
 [],
 [('D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
   0),
  ('D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pygame\\__pyinstaller', 0),
  ('D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('12.jpg', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\12.jpg', 'DATA'),
  ('2.ico', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\2.ico', 'DATA'),
  ('33.png', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\33.png', 'DATA')],
 '3.8.20 (default, Oct  3 2024, 15:19:54) [MSC v.1929 64 bit (AMD64)]',
 [('pyi_rth_pyqt5',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('gui_app',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui_app.py',
   'PYSOURCE')],
 [('inspect', 'D:\\Miniconda3\\envs\\py38\\lib\\inspect.py', 'PYMODULE'),
  ('importlib',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Miniconda3\\envs\\py38\\lib\\contextlib.py', 'PYMODULE'),
  ('configparser',
   'D:\\Miniconda3\\envs\\py38\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Miniconda3\\envs\\py38\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\Miniconda3\\envs\\py38\\lib\\py_compile.py', 'PYMODULE'),
  ('lzma', 'D:\\Miniconda3\\envs\\py38\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\Miniconda3\\envs\\py38\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\Miniconda3\\envs\\py38\\lib\\bz2.py', 'PYMODULE'),
  ('_strptime', 'D:\\Miniconda3\\envs\\py38\\lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'D:\\Miniconda3\\envs\\py38\\lib\\datetime.py', 'PYMODULE'),
  ('calendar', 'D:\\Miniconda3\\envs\\py38\\lib\\calendar.py', 'PYMODULE'),
  ('threading', 'D:\\Miniconda3\\envs\\py38\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Miniconda3\\envs\\py38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'D:\\Miniconda3\\envs\\py38\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\Miniconda3\\envs\\py38\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Miniconda3\\envs\\py38\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Miniconda3\\envs\\py38\\lib\\gzip.py', 'PYMODULE'),
  ('copy', 'D:\\Miniconda3\\envs\\py38\\lib\\copy.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Miniconda3\\envs\\py38\\lib\\fnmatch.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Miniconda3\\envs\\py38\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\Miniconda3\\envs\\py38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Miniconda3\\envs\\py38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email', 'D:\\Miniconda3\\envs\\py38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket', 'D:\\Miniconda3\\envs\\py38\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Miniconda3\\envs\\py38\\lib\\selectors.py', 'PYMODULE'),
  ('random', 'D:\\Miniconda3\\envs\\py38\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'D:\\Miniconda3\\envs\\py38\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\Miniconda3\\envs\\py38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Miniconda3\\envs\\py38\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Miniconda3\\envs\\py38\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Miniconda3\\envs\\py38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'D:\\Miniconda3\\envs\\py38\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'D:\\Miniconda3\\envs\\py38\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Miniconda3\\envs\\py38\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Miniconda3\\envs\\py38\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Miniconda3\\envs\\py38\\lib\\gettext.py', 'PYMODULE'),
  ('quopri', 'D:\\Miniconda3\\envs\\py38\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\Miniconda3\\envs\\py38\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\Miniconda3\\envs\\py38\\lib\\optparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Miniconda3\\envs\\py38\\lib\\textwrap.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Miniconda3\\envs\\py38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'D:\\Miniconda3\\envs\\py38\\lib\\csv.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse', 'D:\\Miniconda3\\envs\\py38\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Miniconda3\\envs\\py38\\lib\\ast.py', 'PYMODULE'),
  ('token', 'D:\\Miniconda3\\envs\\py38\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Miniconda3\\envs\\py38\\lib\\tokenize.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Miniconda3\\envs\\py38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'D:\\Miniconda3\\envs\\py38\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Miniconda3\\envs\\py38\\lib\\opcode.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Miniconda3\\envs\\py38\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Miniconda3\\envs\\py38\\lib\\zipimport.py', 'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('stringprep', 'D:\\Miniconda3\\envs\\py38\\lib\\stringprep.py', 'PYMODULE'),
  ('typing', 'D:\\Miniconda3\\envs\\py38\\lib\\typing.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Miniconda3\\envs\\py38\\lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Miniconda3\\envs\\py38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('gui.main_window',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\main_window.py',
   'PYMODULE'),
  ('gui',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\__init__.py',
   'PYMODULE'),
  ('gui.donate_window',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\donate_window.py',
   'PYMODULE'),
  ('gui.help_window',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\help_window.py',
   'PYMODULE'),
  ('gui.system_tray',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\system_tray.py',
   'PYMODULE'),
  ('gui.managers',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\managers.py',
   'PYMODULE'),
  ('gui.config_loader',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\gui\\config_loader.py',
   'PYMODULE'),
  ('config', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\config.py', 'PYMODULE'),
  ('common.log',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\common\\log.py',
   'PYMODULE'),
  ('common', '-', 'PYMODULE'),
  ('common.const',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\common\\const.py',
   'PYMODULE'),
  ('json', 'D:\\Miniconda3\\envs\\py38\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\Miniconda3\\envs\\py38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Miniconda3\\envs\\py38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Miniconda3\\envs\\py38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('subprocess', 'D:\\Miniconda3\\envs\\py38\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\Miniconda3\\envs\\py38\\lib\\signal.py', 'PYMODULE')],
 [('python38.dll', 'D:\\Miniconda3\\envs\\py38\\python38.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_lzma.pyd', 'D:\\Miniconda3\\envs\\py38\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Miniconda3\\envs\\py38\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Miniconda3\\envs\\py38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Miniconda3\\envs\\py38\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Miniconda3\\envs\\py38\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Miniconda3\\envs\\py38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Miniconda3\\envs\\py38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\Miniconda3\\envs\\py38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Miniconda3\\envs\\py38\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\Miniconda3\\envs\\py38\\MSVCP140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Miniconda3\\envs\\py38\\python3.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\Miniconda3\\envs\\py38\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Miniconda3\\envs\\py38\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY')],
 [],
 [],
 [('12.jpg', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\12.jpg', 'DATA'),
  ('2.ico', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\2.ico', 'DATA'),
  ('33.png', 'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\33.png', 'DATA'),
  ('base_library.zip',
   'E:\\Desktop\\123\\企业微信\\dify-on-wechat\\build\\企业微信自动回复\\base_library.zip',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.44.0.dist-info\\RECORD',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\wheel-0.44.0.dist-info\\RECORD',
   'DATA'),
  ('elevenlabs-2.10.0.dist-info\\REQUESTED',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\elevenlabs-2.10.0.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-24.2.0.dist-info\\licenses\\LICENSE',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\attrs-24.2.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\REQUESTED',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\REQUESTED',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('websockets-13.1.dist-info\\RECORD',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\websockets-13.1.dist-info\\RECORD',
   'DATA'),
  ('attrs-24.2.0.dist-info\\METADATA',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\attrs-24.2.0.dist-info\\METADATA',
   'DATA'),
  ('websockets-13.1.dist-info\\LICENSE',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\websockets-13.1.dist-info\\LICENSE',
   'DATA'),
  ('websockets-13.1.dist-info\\top_level.txt',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\websockets-13.1.dist-info\\top_level.txt',
   'DATA'),
  ('elevenlabs-2.10.0.dist-info\\LICENSE',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\elevenlabs-2.10.0.dist-info\\LICENSE',
   'DATA'),
  ('elevenlabs-2.10.0.dist-info\\RECORD',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\elevenlabs-2.10.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.44.0.dist-info\\WHEEL',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\wheel-0.44.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-13.1.dist-info\\METADATA',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\websockets-13.1.dist-info\\METADATA',
   'DATA'),
  ('elevenlabs-2.10.0.dist-info\\INSTALLER',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\elevenlabs-2.10.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-24.2.0.dist-info\\RECORD',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\attrs-24.2.0.dist-info\\RECORD',
   'DATA'),
  ('elevenlabs-2.10.0.dist-info\\METADATA',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\elevenlabs-2.10.0.dist-info\\METADATA',
   'DATA'),
  ('websockets-13.1.dist-info\\INSTALLER',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\websockets-13.1.dist-info\\INSTALLER',
   'DATA'),
  ('elevenlabs-2.10.0.dist-info\\WHEEL',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\elevenlabs-2.10.0.dist-info\\WHEEL',
   'DATA'),
  ('attrs-24.2.0.dist-info\\INSTALLER',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\attrs-24.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.44.0.dist-info\\METADATA',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\wheel-0.44.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-24.2.0.dist-info\\REQUESTED',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\attrs-24.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.44.0.dist-info\\LICENSE.txt',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\wheel-0.44.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-24.2.0.dist-info\\WHEEL',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\attrs-24.2.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-13.1.dist-info\\WHEEL',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\websockets-13.1.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.44.0.dist-info\\entry_points.txt',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\wheel-0.44.0.dist-info\\entry_points.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'D:\\Miniconda3\\envs\\py38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA')])
